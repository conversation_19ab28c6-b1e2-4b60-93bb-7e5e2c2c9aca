import '@webcomponents/webcomponentsjs/webcomponents-bundle'
import { createRoot } from 'react-dom/client'
import './index.css'
import { App } from './app'
import { initI18n } from '../common/i18n'
import 'animate.css'
import { Provider } from 'react-redux'
import getStoreConfig from '../common/local_store'
import { PersistGate } from 'redux-persist/integration/react'
import { SettingsProvider } from '@/common/store/settings'
import { StyleProvider } from '@ant-design/cssinjs'
import Cache from '@ant-design/cssinjs/es/Cache'
import { conatinerId, tag } from './shadow-dom'
import { isFunBlocks } from './utils/content-dom'

const { store, persistor } = getStoreConfig()

// second, create custom Cache entity
class CustomCache extends Cache {
  override update(keys, valFn) {
    const shadowRootElement = document.getElementsByTagName(tag)[0]
    const shadowRoot = shadowRootElement?.shadowRoot

    if (!shadowRoot) {
      console.warn(`Shadow root not found for tag: ${tag}`)
      return
    }

    let path = keys.join('%')
    let prevValue = this.cache.get(path)!
    let nextValue = valFn(prevValue)

    // 为 antd 组件添加高 z-index 样式
    if (
      nextValue.includes('.ant-tooltip') ||
      nextValue.includes('.ant-popover') ||
      nextValue.includes('.ant-select-dropdown') ||
      nextValue.includes('.ant-modal')
    ) {
      nextValue = nextValue.replace(/z-index:\s*\d+/g, 'z-index: **********')
      if (!nextValue.includes('z-index')) {
        nextValue = nextValue.replace(
          /\{([^}]*)\}/,
          '{ z-index: **********; $1 }'
        )
      }
    }

    let id = keys.join('-')
    let style = shadowRoot.getElementById(id)
    if (!style) {
      style = document.createElement('style')
      style.id = id
      shadowRoot.appendChild(style)
    }
    style.innerText = nextValue
    super.update(keys, valFn)
  }
}

const render = async () => {
  let container = document.getElementsByTagName(tag)[0]

  if (!container) {
    console.warn(
      `Container element with tag ${tag} not found. Creating a new one.`
    )
    container = document.createElement(tag)
    container.className = 'funblocks-container'
    document.documentElement.append(container)
  }

  //Todo: Following code can open right space for AI dialog!!!
  // let style = document.createElement('style');
  // style.id = 'funblocks-right-space-style';
  // style.innerHTML = `
  // html {
  //   width: calc(100% - 450px) !important;
  //   position: relative !important;
  //   min-height: 100vh !important;
  // }
  // `
  // document.getElementsByTagName('head')[0].appendChild(style)

  await initI18n()

  // 等待 shadow DOM 创建完成
  const waitForShadowRoot = () => {
    return new Promise<ShadowRoot>((resolve) => {
      const checkShadowRoot = () => {
        const shadowRoot = container.shadowRoot
        if (shadowRoot) {
          resolve(shadowRoot)
        } else {
          setTimeout(checkShadowRoot, 100)
        }
      }
      checkShadowRoot()
    })
  }

  const shadowRoot = await waitForShadowRoot()
  const rootElement = shadowRoot.querySelector(`#${conatinerId}`)

  if (!rootElement) {
    console.error(`Root element with id ${conatinerId} not found in shadow DOM`)
    return
  }

  createRoot(rootElement).render(
    <Provider store={store}>
      <PersistGate loading={null} persistor={persistor}>
        <StyleProvider
          cache={new CustomCache('funblocks-shadow-dom')}
          container={shadowRoot as any}
        >
          <SettingsProvider>
            <App />
          </SettingsProvider>
        </StyleProvider>
      </PersistGate>
    </Provider>
  )
}

const initializePlugin = (reason: string) => {
  if (!isFunBlocks) {
    render().catch((error) => console.error('Error rendering app:', error))
  }
}

// 监听来自背景脚本的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.action === 'PLUGIN_ACTIVATED') {
    initializePlugin(message.reason)
  }
})

// 在页面加载完成后初始化插件
if (!isFunBlocks) {
  window.addEventListener('load', () => {
    setTimeout(() => initializePlugin('page_load'), 2000)
  })
}
