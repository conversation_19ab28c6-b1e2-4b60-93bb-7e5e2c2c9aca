import { Popup } from './container/index'

import { useSettings } from '../common/store/settings'
import messages from '../common/messages'
import { IntlProvider } from 'react-intl'
import ScreenCapture from './container/slidebar-menu/screen-capture'
import { useEffect, useState } from 'react'
import ScreenshotOverlay from './ScreenshortOverlay'
import { createRoot } from 'react-dom/client'
import ScreenshotCapture from './ScreenshotCapture'

export const App: React.FC = () => {
  const { settings } = useSettings()
  const lng = settings?.lang || 'en'

  const [screenCapture, setScreenCapture] = useState()

  const handleScreenCapture = (screenCapture) => {
    setScreenCapture(screenCapture)
  }

  const [screenshot, setScreenshot] = useState(null)

  const handleCapture = (dataUrl) => {
    setScreenshot(dataUrl)
  }

  useEffect(() => {
    if (!screenCapture) return
    const screenCaptureSource = screenCapture
    const downloadLink = document.createElement('a')
    const fileName = 'react-screen-capture.png'

    downloadLink.href = screenCaptureSource
    downloadLink.download = fileName
    downloadLink.click()
  }, [screenCapture])

  // useEffect(() => {
  //   const container = document.createElement('div');
  //               document.body.appendChild(container);
  //               createRoot(container).render(<ScreenshotOverlay />)
  // }, [])

  return (
    <IntlProvider locale={lng} messages={messages[lng]}>
      <div className="text-black">
        {/* <ScreenshotCapture /> */}
        {/* <ScreenCapture  onEndCapture={(dataUrl) => {
        const downloadLink = document.createElement('a');
        const fileName = 'react-screen-capture.png';
        downloadLink.href = dataUrl;
        downloadLink.download = fileName;
        downloadLink.click();
      }}/> */}
        {/* {screenshot && (
        <img src={screenshot} alt="Screenshot" />
      )} */}
        {/* <ScreenshotOverlay /> */}
        {/* <ScreenCapture onEndCapture={handleScreenCapture}> */}
        <Popup />
        {/* </ScreenCapture> */}
        {/*         
        <div style={{width: 400, position: 'fixed', right: 0, bottom: 0, top: 0}}>
          hello world
        </div> */}
      </div>
    </IntlProvider>
  )
}
