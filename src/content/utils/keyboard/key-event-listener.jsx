
const isTriggered = (key, text, trigger) => {
    if (!trigger) return false;

    if (trigger[0] === '/' && (text?.endsWith(':/') || text?.endsWith('://'))) {
      return false;
    }

    if (trigger.length === 1) {
      return key === trigger && text[text.length - 1] === trigger;
    }

    return key === trigger[1] && text[text.length - 1] === trigger[1] && text?.endsWith(trigger);
  };


export const onKeyPressEvent = (e, trigger, callback) => {
    const selection = window.getSelection();
    const focus_at = e.target.selectionStart || selection.focusOffset
    const value = (e.target.value || selection.focusNode?.textContent)?.substring(0, focus_at);

    if (isTriggered(e.key, value, trigger)) {
        callback(e, focus_at, trigger)
    }
  };
