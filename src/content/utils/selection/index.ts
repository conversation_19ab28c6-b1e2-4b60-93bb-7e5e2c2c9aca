import { getSetting } from '@/common/store/settings'
import { onKeyPressEvent } from '../keyboard/key-event-listener'
import { Highlight } from './highlight'
import { debounce } from 'lodash-es'
import { Readability, isProbablyReaderable } from '@mozilla/readability'
import { getContentDom } from '../content-dom'
import { isGoogleDocs, isMicrosoftDocs } from '../content-dom'
import { extractWebContent } from '@/common/article_extractor'
import rangy from 'rangy'
import 'rangy/lib/rangy-core'
import 'rangy/lib/rangy-classapplier'
import 'rangy/lib/rangy-selectionsaverestore'
import { ProductFeatures } from '@/common/product-features'

const properties = [
  'direction',
  'boxSizing',
  'width',
  'height',
  'overflowX',
  'overflowY',
  'borderTopWidth',
  'borderRightWidth',
  'borderBottomWidth',
  'borderLeftWidth',
  'paddingTop',
  'paddingRight',
  'paddingBottom',
  'paddingLeft',
  'fontStyle',
  'fontVariant',
  'fontWeight',
  'fontStretch',
  'fontSize',
  'fontSizeAdjust',
  'lineHeight',
  'fontFamily',
  'textAlign',
  'textTransform',
  'textIndent',
  'textDecoration',
  'letterSpacing',
  'wordSpacing',
]

export class SelectionManager {
  protected selectChangeHandlers = []
  // lock a selction, means when selection change, we won't emit onSelectionChange
  protected locked = false
  protected selection: Selection
  protected highlight: Highlight
  protected savedRange
  protected generatedText: string
  protected editOption: string // replace_only; : insert_only
  protected pageLanguage: string
  private trigger: string
  private textPasted: boolean
  private triggerEvent

  private selectedContent = { text: '' }
  private selectedContentForAI = { text: '' }
  // public text: string = ''
  public position: { x: number; y: number } = {
    x: window.innerWidth / 2 - 240,
    y: window.innerHeight / 3,
  }

  constructor() {
    if (!ProductFeatures.isContextualMenuEnabled()) return

    rangy.init()

    this.setup()
    this.highlight = new Highlight()
  }

  get activeSelection() {
    return this.selection
  }

  public setPageLanguage(lng) {
    this.pageLanguage = lng
  }
  public getPageLanguage() {
    return this.pageLanguage
  }

  get isLocked() {
    return this.locked
  }

  public getTriggerEvent() {
    return this.triggerEvent
  }

  public setTriggerEvent(event) {
    this.triggerEvent = event
  }

  private saveRange(range) {
    // console.log('save range.......', range.range, range)
    this.savedRange = range
  }

  public onSelectionChange = (cb: (selectedText: String) => void) => {
    this.selectChangeHandlers.push(cb)

    return () => {
      this.selectChangeHandlers = this.selectChangeHandlers.filter(
        (handler) => handler != cb
      )
    }
  }

  public setLock(locked: boolean) {
    if (this.locked === locked) {
      return
    }

    this.locked = locked

    if (!locked) {
      this.textPasted = false
    }
  }

  public getEditOption() {
    return this.editOption
  }

  public setGeneratedText(text) {
    this.generatedText = text
  }

  public async append(text?: string, replace?: boolean) {
    // if (isGoogleDocs) {
    //   // document.execCommand('insertText', false, text)
    //   // document.execCommand('paste')
    //   return
    // }

    this.restoreRange()

    const inputNode = this.savedRange.target
    // console.log('input node............', this.savedRange, this.selection)
    if (inputNode) {
      if (!replace) {
        text = (this.selectedContent?.text || '') + text
        // inputNode.setSelectionRange(this.savedRange.target.selectionEnd, this.savedRange.target.selectionEnd)
      }

      // ;(inputNode as any).focus()
      return document.execCommand('insertText', false, text)
    } else {
      if (!replace) {
        if (!!this.trigger) {
          this.select_trigger(this.trigger)
        } else {
          // const range = document.createRange()
          // range.setStart(this.selection.focusNode, this.selection.focusOffset)
          // range.setEnd(this.selection.focusNode, this.selection.focusOffset)
          // this.selection.removeAllRanges()
          // this.selection.addRange(range)

          this.selection.collapseToEnd()
        }
        const container = this.selection.getRangeAt(0).commonAncestorContainer
        container?.parentNode?.focus?.()
      }

      // document.execCommand('delete')
      // don't know why. but at first time settimeout excute paste actions, everything works as expected
      if (this.textPasted) {
        document.execCommand('paste')
      } else {
        setTimeout(() => {
          document.execCommand('paste')
          this.textPasted = true
        }, 100)
      }
    }
  }

  public replace(text?: string) {
    this.append(text, true)
  }

  private findFirstTextNode(node) {
    if (node.nodeType === Node.TEXT_NODE) {
      return node
    }

    for (let i = 0; i < node.childNodes.length; i++) {
      const result = this.findFirstTextNode(node.childNodes[i])
      if (result) {
        return result
      }
    }

    return node
  }

  public select_from_start(target, trigger) {
    //Todo: verify any bugs after remove following codes
    // if (!index) {
    //   // e.target.select()
    //   return
    // }

    this.trigger = trigger

    if (target && target.tagName?.toLowerCase() != 'div') {
      if (!this.locked) {
        this.savedRange = {
          target,
          start: 0,
          end: target.selectionStart,
        }
        this.setSelectedContent({
          text: target?.value?.substring(
            0,
            target.selectionStart - trigger.length
          ),
          isEditable: true,
        })
      }
      target.setSelectionRange(0, target.selectionStart)
      this.selection = window.getSelection()
    } else {
      const range = document.createRange()
      const selection = window.getSelection()

      if (target?.contains(selection.focusNode)) {
        let node = selection.focusNode
        while (node && node.tagName?.toLowerCase() !== 'html') {
          if (['p', 'div'].includes(node.tagName?.toLowerCase())) {
            break
          }

          if (node.parentNode) {
            node = node.parentNode
          } else {
            break
          }
        }

        range.setStart(
          this.findFirstTextNode(node) || node.firstChild || node,
          0
        )
        range.setEnd(selection.focusNode, selection.focusOffset)
      } else {
        // if (this.findFirstTextNode(target) instanceof Node)
        range.setStart(target, 0)
        range.setEnd(target, 0)
      }

      selection.removeAllRanges()
      selection.addRange(range)

      this.selection = selection

      if (!this.locked) {
        selection.rangeCount &&
          this.saveRange({
            range: selection.getRangeAt(0).cloneRange(),
            // rangeInfo: {
            //   anchorNode: selection.anchorNode,
            //   anchorOffset: selection.anchorOffset,
            //   focusNode: selection.focusNode,
            //   focusOffset: selection.focusOffset
            // }
          })

        const selectedText = selection.toString()
        this.setSelectedContent({
          text: selectedText?.substring(
            0,
            selectedText.length - trigger.length
          ),
          isEditable: true,
        })

        // console.log('selected text.........', selectedText, this.selectedContent)

        if (isMicrosoftDocs) {
          this.editOption = 'insert_only'
        }
      }
    }
  }

  private select_trigger(trigger) {
    if (this.selection.focusOffset < trigger.length) {
      return
    }

    const range = document.createRange()
    const selection = this.selection

    range.setStart(selection.focusNode, selection.focusOffset - trigger.length)
    range.setEnd(selection.focusNode, selection.focusOffset)

    selection.removeAllRanges()
    selection.addRange(range)
  }

  private getSelectionText() {
    // const selectedText = rangy.getSelection().toString().replace(/\u200B/g, '');
    // return selectedText
    if (window.getSelection) {
      try {
        var activeElement = document.activeElement

        if (
          activeElement &&
          activeElement.value &&
          ['textarea'].includes(activeElement.target?.tagName?.toLowerCase())
        ) {
          // firefox bug https://bugzilla.mozilla.org/show_bug.cgi?id=85686
          return activeElement.value.substring(
            activeElement.selectionStart,
            activeElement.selectionEnd
          )
        } else {
          return window.getSelection().toString()
        }
      } catch (e) {}
    } else if (document.selection && document.selection.type != 'Control') {
      // For IE
      return document.selection.createRange().text
    }
  }

  private save_selection(e) {
    if (window.getSelection()) {
      try {
        var activeElement = document.activeElement
        if (
          activeElement &&
          activeElement.value &&
          ['textarea'].includes(activeElement.target?.tagName?.toLowerCase())
        ) {
          if (!this.locked) {
            this.saveRange({
              target: e.target,
              start: e.target.selectionStart,
              end: e.target.selectionEnd,
            })
          }

          !!e.target.setSelectionRange &&
            e.target.setSelectionRange(
              activeElement.selectionStart,
              activeElement.selectionEnd
            )
          // firefox bug https://bugzilla.mozilla.org/show_bug.cgi?id=85686
          // return activeElement.value.substring(activeElement.selectionStart, activeElement.selectionEnd);
        } else if (!isGoogleDocs) {
          if (!this.locked && window.getSelection()?.rangeCount > 0) {
            this.saveRange({
              range: window.getSelection().getRangeAt(0).cloneRange(),
            })
          }
        }

        return window.getSelection()
      } catch (err) {
        console.log('err in get selection', err)
      }
    }

    return e.target?.ownerDocument?.getSelection()
  }

  private isSelectionEditable(e, selection, selectedText) {
    if (['input', 'textarea'].includes(e.target.tagName.toLowerCase())) {
      return true
    }

    if (!selection.rangeCount) {
      return false
    }

    var range = selection.getRangeAt(0)
    var container =
      !!selectedText && range ? range.commonAncestorContainer : e.target

    // Check if the common ancestor container or any of its ancestors are editable
    while (container) {
      if (
        container.nodeType === 1 &&
        (container.isContentEditable ||
          container.tagName.toLowerCase() === 'textarea' ||
          (container.tagName.toLowerCase() === 'input' &&
            container.type !== 'hidden'))
      ) {
        return true // Editable element found
      }

      container = container.parentNode
    }

    return false // No editable element found
  }

  private getCaretCoordinates(element, position) {
    const div = document.createElement('div')
    const style = div.style
    const computed = window.getComputedStyle(element)

    style.whiteSpace = 'pre-wrap'
    style.wordWrap = 'break-word'
    style.position = 'absolute'
    style.visibility = 'hidden'

    // 复制textarea的样式
    properties.forEach((prop) => {
      style[prop] = computed[prop]
    })

    div.textContent = element.value.substring(0, position)

    const span = document.createElement('span')
    span.textContent = element.value.substring(position) || '.'
    div.appendChild(span)

    document.body.appendChild(div)
    const coordinates = {
      top: span.offsetTop + parseInt(computed['borderTopWidth']),
      left: span.offsetLeft + parseInt(computed['borderLeftWidth']),
    }
    document.body.removeChild(div)

    return coordinates
  }

  private async setup() {
    // debounce the event
    const eventHandler = debounce(async (e) => {
      if (e.type === 'mouseup' || (e.type === 'keyup' && e.key === 'Shift')) {
        if (['input', 'select'].includes(e.target?.tagName?.toLowerCase())) {
          return
        }

        if (e.target?.tagName?.toLowerCase().includes('funblocks')) {
          return
        }

        const ancestorWithId = e.target?.closest('[id^="funblocks"]')
        if (ancestorWithId) {
          // console.log('found ancester node.........', ancestorWithId)
          return
        }

        let selectedText = this.getSelectionText(e)
        if (!selectedText && isGoogleDocs) {
          // navigator.clipboard.readText().then((clipText) => this.clipboardText == clipText)
          let clipboardText = await navigator.clipboard.readText()
          document
            .querySelector('.docs-texteventtarget-iframe')
            .contentDocument.execCommand('copy')
          selectedText = document.querySelector('.docs-texteventtarget-iframe')
            .contentDocument.body.innerText

          navigator.clipboard.writeText(clipboardText)

          if (this.generatedText) {
            setTimeout(() => {
              navigator.clipboard.writeText(this.generatedText)
            }, 200)
          }
        }

        selectedText = selectedText?.replace(/\u200B/g, '')
        // console.log('selected text..............', selectedText)

        this.selection = this.save_selection(e)
        let isEditable = this.isSelectionEditable(
          e,
          this.selection,
          selectedText
        )

        if (!this.locked || !selectedText) {
          this.setSelectedContent({
            text: selectedText,
            isEditable: isEditable || !selectedText,
          })
          this.trigger = null

          if (isMicrosoftDocs) {
            this.editOption = 'replace_only'
          }
        }

        if (!!selectedText?.trim()) {
          // this.position = {
          //   x: Math.max(e.x, 10),
          //   y: e.y + 10,
          // }
          // console.log('this.selection...............', this.selection.rangeCount,  this.selection.rangeCount && this.selection.getRangeAt(0), this.selection)
          if (this.selection.rangeCount > 0) {
            var range = this.selection.getRangeAt(0)
            var rect = range.getBoundingClientRect()

            if (
              ['input', 'textarea'].includes(e.target.tagName.toLowerCase())
            ) {
              // rect = range.endContainer.getBoundingClientRect()

              const coordinates = this.getCaretCoordinates(
                e.target,
                Math.max(e.target.selectionStart || e.target.selectionEnd)
              )

              // 获取textarea的位置
              const r = e.target.getBoundingClientRect()

              rect = {
                left: r.left + coordinates.left,
                bottom: r.top + coordinates.top + 28,
                width: 0,
              }
            }

            this.position = {
              x: rect.left + rect.width / 3,
              y:
                rect.bottom + 50 < window.innerHeight
                  ? rect.bottom + 10
                  : window.innerHeight - 40,
            }
          }
        } else {
          this.rePositionToCenter()
        }
      }

      this.setTriggerEvent(e)
      this.selectChangeHandlers.forEach((handler) =>
        handler(this.selectedContent)
      )
    }, 300)

    // listen keyup and check if there is a selection
    document.addEventListener('mouseup', eventHandler, true)
    document.addEventListener('keyup', eventHandler, true)

    // bind mouseup for every iframes
    const iframes = [...document.getElementsByTagName('iframe')]
    iframes.forEach((f) => {
      if (f.contentDocument) {
        f.contentDocument.addEventListener('mouseup', eventHandler, true)
        f.contentDocument.addEventListener('keyup', eventHandler, true)
      }
    })
  }
  public rePositionToCenter() {
    this.position = {
      x: window.innerWidth / 2 - 240,
      y: window.innerHeight / 3,
    }
  }

  public rePositionToTopLeft() {
    this.position = {
      x: 0,
      y: 0,
    }
  }

  public setSelectedContent(content) {
    this.selectedContent = content
  }

  public getSelectedContent() {
    return this.selectedContent
  }

  public setSelectedContentForAI(content) {
    this.selectedContentForAI = content
  }

  public getSelectedContentForAI() {
    return this.selectedContentForAI
  }

  public async selectWholePage() {
    var article = await extractWebContent()

    if (!article?.textContent) {
      this.setSelectedContent({})
    } else {
      this.setSelectedContent({
        type: 'article',
        length: article.length,
        title: article.title,
        text: article.textContent,
        excerpt: article.excerpt,
      })
    }
  }

  public async extractMeaningfulContent(dom, min_length = 300) {
    const article = await extractWebContent(dom)

    // console.log('meaningful content.............', article)
    let meaningfulContent
    if (article?.length > min_length) {
      meaningfulContent = {
        type: 'article',
        length: article.length,
        title: article.title,
        text: article.textContent,
      }
    } else {
      meaningfulContent = null
    }

    return meaningfulContent
  }

  private restoreRange() {
    if (!this.savedRange) return

    if (this.savedRange.range) {
      // console.log('selection........', this.selection, this.savedRange.range)
      this.selection.removeAllRanges()
      this.selection.addRange(this.savedRange.range)

      // clone a new copy, to prevent it's being altered
      this.saveRange({ range: this.savedRange.range.cloneRange() })
      // console.log('selected............0', this.selection)
    } else if (this.savedRange.rangeInfo) {
      const range = document.createRange()
      const selection = window.getSelection()
      // console.log('selection........', selection, this.savedRange.rangeInfo)
      range.setStart(
        this.savedRange.rangeInfo.anchorNode,
        this.savedRange.rangeInfo.anchorOffset
      )
      range.setEnd(
        this.savedRange.rangeInfo.focusNode,
        this.savedRange.rangeInfo.focusOffset
      )

      selection.removeAllRanges()
      selection.addRange(range)
      // console.log('selected............', window.getSelection())
    } else if (this.savedRange.target) {
      this.savedRange.target.focus()
      this.savedRange.target.setSelectionRange(
        this.savedRange.start,
        this.savedRange.end
      )
    }
  }
}
