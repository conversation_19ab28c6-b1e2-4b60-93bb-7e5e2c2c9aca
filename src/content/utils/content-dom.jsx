const hostname = window.location.hostname
export const isFunBlocks = hostname.includes('app.funblocks')
export const isGoogleDocs = hostname.includes('docs.google.com')
export const isMicrosoftDocs = hostname.includes('onedrive.live.com') || hostname.includes('officeapps.live.com')
export const isHotmail = hostname.includes('outlook.live.com')
export const isGmail = hostname.includes('mail.google.com')
export const isReddit = hostname.includes('reddit.com')
export const isLinkedIn = hostname.includes('linkedin.com')
export const isTwitter = hostname.includes('x.com')
export const isYoutube = hostname.includes('youtube.com')
export const isGitHub = hostname.includes('github.com')
export const isTechCrunch = hostname.includes('techcrunch.com')
export const isFacebook = hostname.includes('facebook.com')
export const isProductHunt = hostname.includes('producthunt.com')

export const getDomain = () => {
    var domainParts = hostname.split('.');

    var topLevelDomain = domainParts.length > 1 ? domainParts[domainParts.length - 2] : domainParts[0];
    return topLevelDomain;
}

export const getContentDom = () => {
    if (isHotmail) {
        return document?.querySelector('[role="document"]')
        // const msgBodys = Array.from(document?.querySelectorAll('[id="UniqueMessageBody"]'));
        // if (!msgBodys.length) {
        //     return null;
        // }

        // let msgBody = msgBodys.find(mb => mb.querySelector('table'))
        // if (!msgBody) {
        //     msgBody = msgBodys[msgBodys.length - 1]
        // }

        // return msgBody;
    } else if (isGmail) {
        return Array.from(document.querySelectorAll('[data-message-id]') || []).find(element => element.getAttribute('data-message-id').trim() !== '');
    } else if (isReddit) {
        return document.querySelector('shreddit-post')
    }

    return document.documentElement
}

export const getSender = () => {
    const msgBody = getContentDom();
    if (!msgBody) return;

    if (isHotmail) {
        if (msgBody.parentNode?.firstChild?.childNodes?.length > 1) {
            return msgBody.parentNode.firstChild?.firstChild?.firstChild?.textContent || msgBody.parentNode.firstChild?.textContent
        }
    } else if (isGmail) {
        return findClosestNodeWithSelector(msgBody, 'span[email]')?.querySelector('span[email]')?.textContent
    }
}

export const getTargetEditor = () => {
    if (isHotmail) {
        return document.querySelector('[id^="editor"]')?.querySelector('div[role="textbox"][contenteditable="true"]')
    } else if (isGmail) {
        return document.querySelector('div.editable[role="textbox"][contenteditable="true"]')
    } else if (isReddit) {
        if (document.getElementById('submit-post-button')) {
            return document.querySelector('r-post-composer-form')?.querySelector('shreddit-composer')
                ?.querySelector('[slot="rte"]')
        }
        return document.querySelector('shreddit-composer').querySelector('div[slot="rte"]')
    } else if (isLinkedIn) {
        if (window.location.pathname.includes('article')) {
            return document.querySelector('.article-editor-content')?.querySelector('[contenteditable="true"]')
        }

        return document.querySelector('.share-box')?.querySelector('[contenteditable="true"]')
    } else if (isTwitter) {
        return document.querySelector('.public-DraftEditor-content')
    } else if (isFacebook) {
        const dialog = document.querySelector('[role="dialog"]');
        if (dialog) {
            return dialog.querySelector('[role="textbox"][contenteditable="true"]')
        }

        return document.querySelector('[role="textbox"][contenteditable="true"]')
    } else if (isProductHunt) {
        return document.querySelector('form[data-test="comment-form"]').querySelector('[contenteditable="true"]')
    }

    return null;
}

export const getDrafterInfo = async (targetEditor, selection) => {
    const domain = getDomain()
    let drafter = domain === 'reddit' ? 'reddit post' : domain;
    let main_content;

    if (window.location.hostname.includes("mp.weixin.qq.com") || ['medium', 'jianshu'].includes(domain)) {
        drafter = 'blog'
    } else if (isGmail || isHotmail) {
        drafter = 'email'
    }

    if (domain === 'x') {
        drafter = 'twitter'
    }

    let action = 'draft';
    let post;

    if (drafter === 'email') {
        if (getContentDom() && (isHotmail || isGmail && !document.querySelector('[role="dialog"]')?.contains(targetEditor))) {
            action = 'reply_email';
            let sender = getSender();
            // main_content = selection.extractMeaningfulContent(getContentDom(), 10);
            main_content = {
                text: getContentDom()?.textContent
            }
            main_content.sender = sender;
        }
    } else if (drafter === 'quora') {
        const dialog = document.querySelector('div[role="dialog"][aria-modal="true"]');
        if (dialog) {
            const questionEle = document.querySelector('div.puppeteer_test_question_title');
            if (questionEle) {
                action = 'quora_reply';
                main_content = questionEle.innerText
            }
        } else {
            post = findClosestBigBrotherNodeWithSelector(targetEditor, 'div.puppeteer_test_answer_content')

            if (post) {
                let see_more = post.querySelector('.qt_read_more');
                await clickSeeMore(see_more);

                action = 'reply'
                main_content = post.querySelector('div.puppeteer_test_answer_content').innerText
            }
        }
    } else if (drafter == 'reddit post') {
        const content_dom = getContentDom();
        if (content_dom) {
            main_content = {
                text: content_dom.querySelector('[slot="text-body"]')?.textContent,
                title: content_dom.querySelector('[slot="title"]')?.textContent,
            }

            action = 'social_post_reply'
        }
    } else if (drafter == 'producthunt') {
        const ancestor = targetEditor.closest('form');

        if (ancestor) {
            const infoDom = document.querySelector('[data-test="vote-button"]').closest('section').querySelectorAll('[data-sentry-component="LegacyText"]');

            const title = infoDom[0]?.textContent;
            const tagline = infoDom[1]?.textContent;
            const description = document.querySelector('.prose')?.textContent;
            const productInfo = `[product title]: ${title}\n\n[product tagline]: ${tagline}\n\n[product description]: ${description}`;

            let commentBody;
            if (ancestor.getAttribute('data-test') === 'comment-form') {
                const comments = targetEditor.closest('#comments');
                const threadElement = comments.querySelector('[data-test^="thread"]');
                const comment = threadElement.querySelector('[id^="comment"]')
                commentBody = comment.querySelector('[class^="styles_commentBody"], [class*=" styles_commentBody"]');
                main_content = productInfo + '\n\n[maker/hunter comment]:' + commentBody?.textContent;
                action = 'producthunt_voter_comment'
            } else {
                commentBody = ancestor.parentNode.querySelector('[data-sentry-component="RichText"]');
                main_content = productInfo + '\n\n[voter comment]:' + commentBody?.textContent;
                action = 'producthunt_maker_reply'
            }
        }
    } else if (drafter === 'twitter') {
        const dialog = document.querySelector('div[role="dialog"][aria-modal="true"]');
        if (dialog) {
            post = dialog.querySelector('article');
            if (post) {
                action = 'social_post_reply';
                main_content = post.querySelector('[data-testid="tweetText"]')?.innerText
            }
        } else {
            if (window.location.pathname?.includes('status')) {
                action = 'social_post_reply';
                main_content = document.querySelector('article').querySelector('[data-testid="tweetText"]')?.innerText
            }
        }
    } else if (drafter === 'facebook') {
        const dialog = document.querySelector('div[role="dialog"]');

        if (dialog) {
            post = dialog.querySelector('[data-ad-rendering-role="message"]');
        } else {
            post = findClosestNodeWithAttributes(targetEditor, [{ key: "data-ad-rendering-role", value: "message" }])?.querySelector('[data-ad-rendering-role="message"]')
        }
        // console.log('facebook post.......', {dialog, post})

        if (post || dialog.querySelector('[role="article"]')) {
            let see_more = post?.querySelector('[role="button"]');
            await clickSeeMore(see_more);

            action = 'social_post_reply';
            main_content = post?.innerText
        }
    } else if (drafter == 'linkedin') {
        const dialog = document.querySelector('div[role="dialog"]');
        if (window.location.pathname.includes('article')) {
            drafter = 'linkedin article'
        } else if (dialog?.contains(targetEditor)) {
            drafter = 'linkedin share'
        } else {
            post = findClosestBigBrotherNodeWithSelector(targetEditor, 'div.feed-shared-inline-show-more-text').querySelector('div.feed-shared-inline-show-more-text')

            if (post) {
                let see_more = post.querySelector('button');
                await clickSeeMore(see_more)

                main_content = post.textContent
            } else {
                post = document.querySelector('article');
                if (post) {
                    main_content = post.textContent;
                }
            }

            if (main_content) {
                action = 'social_post_reply';
            }
        }
    }

    return { action, drafter, main_content };
}

const clickSeeMore = async (see_more_btn) => {
    if (see_more_btn) {
        const clickEvent = new MouseEvent('click', {
            bubbles: true,
            cancelable: true,
            view: window,
        });

        see_more_btn.dispatchEvent(clickEvent);

        await new Promise(resolve => setTimeout(resolve, 500))
    }
}

const findClosestBigBrotherNodeWithSelector = (node, selector) => {
    var currentElement = node.parentElement;

    while (currentElement) {
        // 在当前节点的其他子孙节点中查找具有指定属性的节点
        var sibling = currentElement.previousElementSibling;
        while (sibling) {
            var matchedSibling = sibling.querySelector(selector);
            if (matchedSibling) {
                return sibling;
            }
            sibling = sibling.previousElementSibling;
        }

        currentElement = currentElement.parentElement; // 移动到下一个祖先节点
    }

    // 如果没有找到符合条件的节点，则返回null
    return null;
}

export const findClosestNodeWithSelector = (node, selector) => {
    var currentElement = node.parentElement;

    while (currentElement) {
        // 在当前节点的其他子孙节点中查找具有指定属性的节点
        var matched = currentElement.querySelector(selector)

        // 如果找到符合条件的节点，则返回它
        if (matched) {
            return currentElement;
        }

        currentElement = currentElement.parentElement; // 移动到下一个祖先节点
    }

    // 如果没有找到符合条件的节点，则返回null
    return null;
}

export const findClosestNodeWithAttributes = (node, attributes) => {
    var currentElement = node.parentElement;

    while (currentElement) {
        // 在当前节点的其他子孙节点中查找具有指定属性的节点
        var hasAttributes = attributes.every(function (attribute) {
            var nodesWithAttribute;
            if (typeof attribute.value != 'object') {
                nodesWithAttribute = currentElement.querySelectorAll('[' + attribute.key + '="' + attribute.value + '"]');
            } else {
                if (attribute.value.or) {
                    nodesWithAttribute = currentElement.querySelectorAll(attribute.value.or.map(val => {
                        return '[' + attribute.key + '="' + val + '"]'
                    }).join(','))
                }
            }
            return nodesWithAttribute?.length > 0;
        });

        // 如果找到符合条件的节点，则返回它
        if (hasAttributes) {
            return currentElement;
        }

        currentElement = currentElement.parentElement; // 移动到下一个祖先节点
    }

    // 如果没有找到符合条件的节点，则返回null
    return null;
}

export const isDescendantOf = (node, selector) => {
    return node.closest(selector)
}

export const getTextIgnoringContentEditableFalse = (node) => {
    let text = '';

    // 使用递归遍历子节点
    node.childNodes.forEach(child => {
        // 如果是文本节点，直接添加文本内容
        if (child.nodeType === Node.TEXT_NODE) {
            text += child.textContent;
        }
        // 如果是元素节点，且 contentEditable 不为 false，则继续递归
        else if (child.nodeType === Node.ELEMENT_NODE && child.contentEditable !== 'false') {
            text += getTextIgnoringContentEditableFalse(child);
        }
    });

    return text;
}