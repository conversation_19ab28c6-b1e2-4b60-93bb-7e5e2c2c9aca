import { Logo } from "@/components/icon"
import { useCallback, useEffect, useLayoutEffect, useRef, useState } from "react";
import { BookReader } from '@styled-icons/boxicons-regular/BookReader'
import { VectorPen } from '@styled-icons/bootstrap/VectorPen'
import { Tooltip } from "antd";
import { useIntl } from "react-intl";
import { useView, action_bar_widget_id } from "../store/view";
import { Settings } from '@styled-icons/material/Settings';
import { isGoogleDocs } from "@/content/utils/content-dom";
import browser from 'webextension-polyfill'
import Widgets from "../ask-funblocks/widgets";
import { Popover, Switch } from "antd";
import { Close, Notes, Subject } from "@styled-icons/material";
import { Message } from '@styled-icons/boxicons-regular/Message';
import { Screenshot } from '@styled-icons/boxicons-regular/Screenshot';
import { useDispatch, useSelector } from "react-redux";
import { CLOSE_WIDGET_DIALOG, QUICK_SETTINGS_DIALOG, SCREEN_CAPTURE } from "@/common/constants/actionTypes";
import { isWidgetClosed } from "../ask-funblocks/widgets/close-widget-modal";
import { useSettings } from "@/common/store/settings";
import { Flowchart } from "@styled-icons/fluentui-system-regular/Flowchart";
import { extractWebContent } from "@/common/article_extractor";
import { funblocks_domain } from "@/common/serverAPIUtil";
import { useSelectionManager } from "../store/selection";
import { getCurrentProduct, ProductFeatures } from "@/common/product-features";
import { TextParagraph } from "@styled-icons/bootstrap";

export const side_bar_widget_id = ProductFeatures.getProductId('side_bar_widget_id');
const product = getCurrentProduct()
const imagePath = browser.runtime.getURL(`assets/${product}-icon.png`);

export const SideBarMenu = () => {
    let isDragging = false;
    const sidebarRef = useRef();
    const sideBar = sidebarRef.current;
    const [hovered, setHovered] = useState();
    const [bottom, setBottom] = useState(100);
    const [resideLeft, setResideLeft] = useState(true)
    const [topDown, setTopDown] = useState(false)
    const [sideBarHeight, setSideBarHeight] = useState(0)
    const [showQuickSettings, setShowQuickSettings] = useState();
    const intl = useIntl();
    const dispatch = useDispatch();
    const selection = useSelectionManager();
    const { openWriterAssistant, openReaderAssistant, goToInputPage, openMindMapAssistant } = useView();
    const { settings, setSettings } = useSettings();
    const closeWidgetModalState = useSelector(state => state.uiState.closeWidgetDialog) || {};
    const [close_widget_modal_state, set_close_widget_modal_state] = useState();
    const screen_capture = useSelector(state => state.uiState.screen_capture)

    const normalzieBottom = useCallback((bottom) => {
        if (!bottom) {
            return 150
        }
        if (bottom > window.innerHeight - 60) {
            bottom = window.innerHeight - 60
        } else if (bottom < 20) {
            bottom = 20
        }

        return bottom
    }, [window.innerHeight])

    const handleMouseDown = (event) => {
        isDragging = true
        const initY = event.clientY
        const current_bottom = window.innerHeight - sideBar.getBoundingClientRect().bottom


        function handleDrag(event) {
            if (isDragging) {
                let bottom = normalzieBottom(current_bottom + initY - event.clientY);

                setBottom(bottom)
            }
        }

        function handleRelease(event) {
            if (isDragging) {
                let bottom = normalzieBottom(current_bottom + initY - event.clientY);
                setSettings({
                    ...settings,
                    sidebar: {
                        ...settings?.sidebar,
                        bottom
                    }
                })
            }

            isDragging = false;
            document.removeEventListener('mousemove', handleDrag);
            document.removeEventListener('mouseup', handleRelease);
            sideBar.removeEventListener('mouseup', handleRelease);
        }

        document.addEventListener('mousemove', handleDrag);
        document.addEventListener('mouseup', handleRelease);
        sideBar.addEventListener('mouseup', handleRelease);
    };

    useEffect(() => {
        dispatch({
            type: SCREEN_CAPTURE,
            value: {
                shot: false,
                crop: false
            }
        })
    }, [])

    useEffect(() => {
        let bottom = normalzieBottom(settings?.sidebar?.bottom);
        setBottom(bottom)
    }, [settings?.sidebar?.bottom, normalzieBottom])

    useEffect(() => {
        setTopDown(bottom > window.innerHeight * 2 / 3)
    }, [bottom, window.innerHeight])

    useEffect(() => {
        setResideLeft(settings?.sidebar?.align == 'left')
    }, [settings?.sidebar?.align])

    useLayoutEffect(() => {
        if (hovered) {
            setSideBarHeight(sideBar.getBoundingClientRect().height)
        }
    }, [hovered])

    useEffect(() => {
        if (closeWidgetModalState?.isModalOpen || ![side_bar_widget_id].includes(closeWidgetModalState.widget) || !closeWidgetModalState.data) {
            return;
        }

        set_close_widget_modal_state(closeWidgetModalState);

        dispatch({
            type: CLOSE_WIDGET_DIALOG,
            value: {
                isModalOpen: false
            }
        })

    }, [closeWidgetModalState])

    return (
        <div
            className="draggable-sidebar"
            style={{
                zIndex: 2147483647,
                rowGap: 10,
                bottom: topDown ? undefined : bottom,
                top: topDown ? window.innerHeight - bottom - sideBarHeight : undefined,
                left: resideLeft ? 2 : undefined,
                right: resideLeft ? undefined : 2,
                flexDirection: topDown ? 'column-reverse' : 'column',
                display: screen_capture?.crop ? 'none' : 'flex'
            }}

            onMouseLeave={() => !showQuickSettings && setHovered(false)}
        >
            {
                hovered && ProductFeatures.isSidebarFeatureEnabled('quick_settings') &&
                <Tooltip title={intl.formatMessage({ id: 'quick_settings' })} placement="left">
                    <div
                        className={`circle_icon_button button_shadow ${ProductFeatures.isFunBlocks() && 'blue_button' || ProductFeatures.isMindMap() && 'light_button'}`}
                        onClick={() => {
                            dispatch({
                                type: QUICK_SETTINGS_DIALOG,
                                value: {
                                    isModalOpen: true
                                }
                            })
                        }}
                    >
                        <Settings size={20} />
                    </div>
                </Tooltip>
            }
            {
                hovered && ProductFeatures.isSidebarFeatureEnabled('screen_capture') &&
                <Tooltip title={intl.formatMessage({ id: 'screenshot' })} placement="left">
                    <div
                        className={`circle_icon_button button_shadow ${ProductFeatures.isFunBlocks() && 'blue_button' || ProductFeatures.isMindMap() && 'light_button'}`}
                        onClick={() => {
                            dispatch({
                                type: SCREEN_CAPTURE,
                                value: {
                                    shot: true,
                                    crop: false
                                }
                            })
                        }}
                    >
                        <Screenshot size={20} />
                    </div>
                </Tooltip>
            }

            {
                hovered &&
                <div
                    className="button_shadow"
                    style={{
                        zIndex: 'inherit',
                        display: 'flex',
                        flexDirection: topDown ? 'column-reverse' : 'column',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: 'white',
                        backgroundColor: ProductFeatures.isFunBlocks() && 'dodgerblue' || ProductFeatures.isMindMap() && 'white',
                        borderRadius: 24,
                        rowGap: 4,
                        width: 32,
                        height: 'fill-content'
                    }}
                >
                    <Tooltip id='ai_flow_question' title={intl.formatMessage({ id: 'ai_flow_question' })} placement="left">
                        <div
                            className={`circle_icon_button ${ProductFeatures.isFunBlocks() && 'blue_button' || ProductFeatures.isMindMap() && 'light_button'}`}
                            onClick={() => {
                                // window.open(`https://app.${funblocks_domain}/#/aiflow?action=initNode&nodeType=prompt&queryType=dynamic`, '_blank')
                                // window.open(`http://localhost:3000/#/aiflow?action=initNode&nodeType=prompt&queryType=dynamic`, '_blank')
                                openMindMapAssistant()
                            }}
                        >
                            <Message size={18} />
                        </div>
                    </Tooltip>
                    <Tooltip id='ai_flow_explore' title={intl.formatMessage({ id: 'ai_flow_explore' })} placement="left">
                        <div
                            className={`circle_icon_button ${ProductFeatures.isFunBlocks() && 'blue_button' || ProductFeatures.isMindMap() && 'light_button'}`}
                            onClick={async () => {
                                const article = await extractWebContent();
                                // window.open(`http://localhost:3000/#/aiflow?queryType=link&action=summary_keypoints&title=${article.title}&content=${article.textContent}&url=${window.location.href}`, '_blank')
                                // window.open(`https://app.${funblocks_domain}/#/aiflow?queryType=link&action=summary_keypoints&title=${article.title}&content=${article.textContent}&url=${window.location.href}`, '_blank')
                                selection.setSelectedContent({
                                    text: article.textContent,
                                    type: 'markdown'
                                })
                                goToInputPage({ objType: 'ril', action: 'flow_mindmap' })
                            }}
                        >
                            <Flowchart size={20} />
                        </div>
                    </Tooltip>
                </div>
            }
            {
                hovered &&
                <div
                    className="button_shadow"
                    style={{
                        zIndex: 'inherit',
                        display: 'flex',
                        flexDirection: topDown ? 'column-reverse' : 'column',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: 'white',
                        backgroundColor: ProductFeatures.isFunBlocks() && 'dodgerblue' || ProductFeatures.isMindMap() && 'white',
                        borderRadius: 24,
                        rowGap: 4,
                        width: 32,
                        height: 'fill-content'
                    }}
                >
                    {
                        ProductFeatures.isSidebarFeatureEnabled('assistant_without_page_context') &&
                        <Tooltip title={intl.formatMessage({ id: 'assistant_without_page_context' })} placement="left">
                            <div
                                className={`circle_icon_button ${ProductFeatures.isFunBlocks() && 'blue_button' || ProductFeatures.isMindMap() && 'light_button'}`}
                                onClick={() => openWriterAssistant(true)}
                            >
                                <Logo size={18} />
                            </div>
                        </Tooltip>
                    }

                    {
                        !isGoogleDocs && ProductFeatures.isSidebarFeatureEnabled('assistant_with_page_context') &&
                        <Tooltip id='assistant_with_page_context' title={intl.formatMessage({ id: 'assistant_with_page_context' })} placement="left">
                            <div
                                className={`circle_icon_button ${ProductFeatures.isFunBlocks() && 'blue_button' || ProductFeatures.isMindMap() && 'light_button'}`}
                                onClick={() => {
                                    openReaderAssistant()
                                }}
                            >
                                <BookReader size={20} />
                            </div>
                        </Tooltip>
                    }

                    {
                        ProductFeatures.isSidebarFeatureEnabled('assistant_summarize') &&
                        <Tooltip title={intl.formatMessage({ id: 'assistant_summarize' })} placement="left">
                            <div
                                className={`circle_icon_button ${ProductFeatures.isFunBlocks() && 'blue_button' || ProductFeatures.isMindMap() && 'light_button'}`}
                                onClick={async () => {
                                    const article = await extractWebContent();
                                    selection.setSelectedContent({
                                        text: article.textContent,
                                        type: 'markdown'
                                    })
                                    goToInputPage({ objType: 'markdown', action: 'summary' })
                                }}
                            >
                                <Notes size={18} />
                            </div>
                        </Tooltip>
                    }
                </div>
            }
            {
                !isWidgetClosed(side_bar_widget_id, settings, close_widget_modal_state) &&
                <div
                    ref={sidebarRef}

                    style={{
                        zIndex: 'inherit',
                        position: 'relative'
                    }}
                    onMouseDown={handleMouseDown}
                    onMouseEnter={() => setHovered(true)}
                >
                    <img width={32} height={32} src={imagePath} draggable={false} style={{ backgroundColor: 'white', borderRadius: 5 }} />
                    {
                        hovered &&
                        <div style={{
                            width: 16,
                            height: 16,
                            borderRadius: 20,
                            position: 'absolute',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            right: resideLeft ? -8 : undefined,
                            left: resideLeft ? undefined : -8,
                            bottom: 28,
                            color: 'white',
                            backgroundColor: 'rgba(108,108,108, 0.6)',
                            zIndex: 'inherit'
                        }}
                            onClick={() => {
                                dispatch({
                                    type: CLOSE_WIDGET_DIALOG,
                                    value: {
                                        isModalOpen: true,
                                        widget: side_bar_widget_id
                                    }
                                })
                            }}
                        >
                            <Close size={12} />
                        </div>
                    }
                </div>
            }
            <Widgets />
        </div>
    )
}

const styles = {
    item: {
        flexDirection: 'row',
        alignItems: 'center',
        display: 'flex',
        paddingTop: '16px',
        columnGap: '10px',
    },
    title: {
        fontWeight: 500,
        fontSize: 15,
    },
    input: {
        padding: '3px 12px',
        border: '1px solid #ccc',
        borderRadius: 15,
        outline: 'none',
        minWidth: '400px',
        fontSize: 14,
    },
    selector: {
        border: '1px solid #ccc',
        paddingLeft: 12,
        paddingRight: 6,
        borderRadius: 15,
        width: 60,
    },
}