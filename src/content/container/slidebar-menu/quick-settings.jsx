import { Divider } from "@mui/material";
import { useIntl } from "react-intl";
import { action_bar_widget_id } from "../store/view";
import { openOptionPage } from '@/common/browser'
import { Modal, Button, Switch, Radio, Space, Tooltip } from "antd";
import { useSettings } from "@/common/store/settings";
import { Triggers } from "@/options/setting-form/quick_compose";
import { Selector } from "@/common/selector";
import { InfoCircle } from '@styled-icons/bootstrap/InfoCircle'
import { useState } from "react";
import { ContextualToolbarSettings } from "@/common/contextual-toolbar-settings";
import { useDispatch, useSelector } from "react-redux";
import { QUICK_SETTINGS_DIALOG } from "@/common/constants/actionTypes";
import { getShadowDOMContainer, getShadowDOMModalContainer } from '@/common/shadow-dom-utils';

export const switch_widget = (widget, checked, settings, setSettings) => {
    let widget_disabled_range = {
        ...settings.widget_disabled_range,
    }
    widget_disabled_range[widget] = {
        ...widget_disabled_range[widget],
        range: !checked ? 'globally' : ''
    };

    // console.log('update settings..........', settings, widget_disabled_range)

    setSettings({
        ...settings,
        widget_disabled_range
    })
}

export const is_widget_switched = (widget, settings) => {
    return settings?.widget_disabled_range[widget]?.range !== 'globally'
}

export const QuickSettings = () => {
    const intl = useIntl();
    const { settings = {}, setSettings } = useSettings();
    const modalState = useSelector(state => state.uiState.quickSettingsDialog) || {};
    const dispatch = useDispatch();

    const { isModalOpen } = modalState;

    const closeModal = () => {
        dispatch({
            type: QUICK_SETTINGS_DIALOG,
            value: {
                isModalOpen: false,
            }
        })
    }

    return <Modal
        title={intl.formatMessage({ id: 'quick_settings' })}
        width={440}
        centered
        open={isModalOpen}
        onCancel={closeModal}
        getContainer={getShadowDOMModalContainer}
        footer={[
            <Button key="back" onClick={closeModal}>
                OK
            </Button>,
        ]}
    >
        <div id="funblocks-quick-settings">
            <div style={{ ...styles.item, paddingTop: 8 }}>
                <span style={styles.title}>
                    {intl.formatMessage({ id: 'settings_ai_trigger' })}
                </span>
                <Selector
                    inputStyle={{
                        ...styles.selector,
                        paddingLeft: 16,

                    }}
                    onChange={(value) => {
                        setSettings({
                            ...settings,
                            trigger: value,
                        })
                    }}
                    value={settings.trigger}
                    options={Triggers.map((t) => ({
                        label: t != 'none' ? t : intl.formatMessage({ id: 'none' }),
                        value: t,
                    }))}
                />
                <Tooltip
                    title={intl.formatMessage({ id: 'settings_ai_trigger_conflict_warn' })}
                    placement="top"
                    getPopupContainer={getShadowDOMContainer}
                >
                    <div style={{ cursor: 'pointer' }}>
                        <InfoCircle size={16} />
                    </div>
                </Tooltip>
            </div>
            <div style={{ ...styles.item, paddingTop: 0 }}>
                <span
                    style={{
                        fontSize: 14,
                        color: '#666',
                    }}
                >
                    {
                        intl.formatMessage(
                            {
                                id:
                                    settings?.trigger && settings?.trigger !== 'none'
                                        ? 'settings_ai_trigger_desc'
                                        : 'settings_ai_trigger_none_desc',
                            },
                            { trigger: "'" + settings.trigger + "'" }
                        )}
                </span>
            </div>

            {/* <div style={styles.item}>
            <span style={styles.title}>
                {intl.formatMessage({ id: 'settings_quick_compose' })}
            </span>
            <Switch size="small" style={{ backgroundColor: is_widget_switched(writerButtonElementId, settings) ? 'dodgerblue' : undefined }} checked={is_widget_switched(writerButtonElementId, settings)} onChange={(checked) => switch_widget(writerButtonElementId, checked, settings, setSettings)} />
        </div> */}

            <ContextualToolbarSettings />
            <Divider style={{ marginTop: 8 }} />

            <div style={{
                color: 'gray',
                display: 'flex',
                flexDirection: 'row',
                columnGap: 4,
                marginTop: 8
            }}>
                {intl.formatMessage({ id: 'view_all' })}
                <div style={{ color: 'dodgerblue', cursor: 'pointer' }}
                    onClick={openOptionPage}
                >
                    {intl.formatMessage({ id: 'settings' })}
                </div>
            </div>
        </div>
    </Modal>
}

const styles = {
    item: {
        flexDirection: 'row',
        alignItems: 'center',
        display: 'flex',
        paddingTop: '16px',
        columnGap: '10px',
    },
    title: {
        fontWeight: 500,
        fontSize: 15,
    },
    input: {
        padding: '3px 12px',
        border: '1px solid #ccc',
        borderRadius: 15,
        outline: 'none',
        minWidth: '400px',
        fontSize: 14,
    },
    selector: {
        border: '1px solid #ccc',
        paddingLeft: 12,
        paddingRight: 6,
        borderRadius: 15,
        width: 60,
    },
}