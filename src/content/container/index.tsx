import { ConfigProvider } from 'antd'
import { useEffect } from 'react'
import { theme } from '../../common/antd-theme'
import { AskFunBlocks, getFixedDom } from './ask-funblocks'
import { SelectionManagerProvider } from './store/selection'
import 'highlight.js/styles/github.css'
import { ViewProvider } from './store/view'
import { InstructionProvider } from './store/instruction'
import EditorListener from './editor-listener'
import { SideBarMenu } from './slidebar-menu'
import { ReplyProvider } from './ask-funblocks/widgets/reply'
import { AIWriterButtonProvider } from './ask-funblocks/widgets/ai-edit-button'
import { SummaryProvider } from './ask-funblocks/widgets/video-summary'
import CloseWidgetModal from './ask-funblocks/widgets/close-widget-modal'
import { QuickSettings } from './slidebar-menu/quick-settings'
import { CodesExpainProvider } from './ask-funblocks/widgets/codes-explain'
import ScreenCapture from '@/content/container/slidebar-menu/screen-capture'
import { LlmAPIKeyModal } from '../../options/setting-form/llm-api-key-modal'

export const Popup: React.FC = () => {
  return (
    <ConfigProvider
      theme={theme}
      getPopupContainer={() => getFixedDom()}
      getTargetContainer={() => getFixedDom()}
    >
      <SelectionManagerProvider>
        <InstructionProvider>
          <ViewProvider>
            <AskFunBlocks />
            {window.self == window.top && (
              <>
                <ReplyProvider>
                  <SummaryProvider>
                    <AIWriterButtonProvider>
                      <CodesExpainProvider>
                        <SideBarMenu />
                      </CodesExpainProvider>
                    </AIWriterButtonProvider>
                  </SummaryProvider>
                </ReplyProvider>
                <CloseWidgetModal />
                <QuickSettings />
                <ScreenCapture />
                <LlmAPIKeyModal />
              </>
            )}
            <EditorListener />
          </ViewProvider>
        </InstructionProvider>
      </SelectionManagerProvider>
    </ConfigProvider>
  )
}
