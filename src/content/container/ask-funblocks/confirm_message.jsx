import { useIntl } from 'react-intl';

import Button from '@mui/material/Button';
import { useEffect, useRef, useState } from 'react';
import { Checkbox } from 'antd';

const ConfirmMessage = ({
  content,
  okText,
  onCancel,
  onConfirm,
  onNeverShow,
  style,
  optionSwitcher,
  optionEnter
}) => {
  const intl = useIntl();

  const [confirmButtonSelected, setConfirmButtonSelected] = useState();

  useEffect(() => {
    optionSwitcher && setConfirmButtonSelected(prevState => {
      return !prevState
    });
  }, [optionSwitcher])

  useEffect(() => {
    if (!optionEnter) return;

    if (confirmButtonSelected) {
      onConfirm();
    } else {
      onCancel();
    }
  }, [optionEnter])

  return (
    <div
      style={{
        ...style
      }}
    >
      <div style={{ fontSize: 14 }}>
        {content}
        {
          !content && intl.formatMessage({ id: 'confirm_delete_content' })
        }
      </div>
      {
        !!onNeverShow &&
        <div className='fill-avaliable' style={{ display: 'flex', flexDirection: 'row', justifyContent: 'flex-end', paddingTop: 6 }}>
          <Checkbox onChange={(event) => onNeverShow(event.target.checked)} ><span style={{ fontSize: 14, color: 'gray' }}> {intl.formatMessage({ id: 'never_show' })}</span> </Checkbox>
        </div>
      }
      <div className='fill-available' style={{ display: 'flex', flexDirection: 'row', justifyContent: 'space-evenly', paddingTop: 10 }}>
        <div className='hoverStand1' style={{ ...styles.button, backgroundColor: confirmButtonSelected ? undefined : '#ddd' }} onClick={onCancel}>{intl.formatMessage({ id: 'cancel' })}</div>
        <div className='hoverStand1' style={{ ...styles.button, backgroundColor: confirmButtonSelected ? '#ddd' : undefined }} onClick={() => {
          onConfirm();
        }}>{okText || intl.formatMessage({ id: 'confirm' })}</div>
      </div>
    </div>
  );
};

const styles = {
  button: {
    border: '1px solid #ddd',
    borderRadius: '15px',
    width: 80,
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    color: '#333',
    padding: 4,
    fontSize: 14,
    fontWeight: 400
  }
}

export default ConfirmMessage;
