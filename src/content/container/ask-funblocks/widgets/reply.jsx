import { EventName } from "@/common/event-name";
import { findClosestNodeWithSelector, findClosestBigBrotherNodeWithSelector, getContentDom, isReddit, isLinkedIn, getTargetEditor, isTwitter, isDescendantOf, isFacebook, isProductHunt } from "@/content/utils/content-dom";
import { isGmail, isHotmail } from "@/content/utils/content-dom";
import { createContainer } from "unstated-next";
import Browser from "webextension-polyfill";
import { getCurrentProduct, ProductFeatures } from "@/common/product-features";

const product = getCurrentProduct()
const imagePath = Browser.runtime.getURL(`assets/${product}-icon.png`);

const { useContainer: useReply, Provider: ReplyProvider } = createContainer(
    () => {
        const replyElementId = ProductFeatures.getProductId('reply-widget');

        const getMessageBodyDom = () => {
            return getContentDom();
        }

        const getHookDomForReply = (node) => {
            let msgBody = getMessageBodyDom();

            if (isHotmail) {
                if (msgBody?.parentNode?.lastChild?.childNodes?.length > 1) {
                    return msgBody.parentNode.lastChild
                }
            } else if (isGmail) {
                if (!msgBody?.nextSibling?.querySelector('form')) {
                    return msgBody?.nextSibling?.querySelector('span[role="link"]')?.parentNode
                }
            } else if (isReddit) {
                if (document.getElementById('submit-post-button')) {
                    return document.getElementById('submit-post-button')?.parentNode;
                }
                return msgBody.parentElement.querySelector('shreddit-composer')?.shadowRoot?.querySelector('div[slot="action-bar-right"]')
            } else if (isLinkedIn) {
                if (window.location.pathname.includes('article')) {
                    return document.querySelector('.article-editor-article-status__container')
                }

                if (node?.parentNode?.querySelector('.comments-comment-box__submit-button--cr')) {
                    return node?.parentNode?.querySelector('.comments-comment-box__submit-button--cr')?.parentNode
                }

                return document.querySelector('.share-box')?.querySelector('.artdeco-carousel__slider')
            } else if (isTwitter) {
                return document.querySelector('[data-testid="toolBar"]')
            } else if (isFacebook) {
                if (node?.querySelector('#focused-state-actions-list')) {
                    return node.querySelector('#focused-state-actions-list').querySelector('ul');
                }

                const dialog = document.querySelector('[role="dialog"]');
                const typeList = dialog?.querySelector('#focused-state-actions-list')?.querySelector('ul');
                if (typeList) {
                    return typeList;
                } else if (dialog) {
                    return dialog.querySelector('[role="presentation"]')?.lastChild
                }
            } 
            // else if (isProductHunt) {
            //     if (window.location.pathname.includes('posts')) {
            //         const commentForm = document.querySelector('form:not([data-test="comment-form"])') || document.querySelector('form[data-test="comment-form"]');
            //         // .querySelector('form[data-test="comment-form"]');
            //         if(commentForm) {
            //             if(commentForm?.querySelector('svg')) {
            //                 return commentForm.querySelector('button[type="submit"]')?.parentNode;
            //             }
            //         } 
            //     }
            // }
        }

        const getTargetEditorForReply = () => {
            return getTargetEditor();
        }

        const getOrignalReplyButtonDom = (currentNode) => {
            if (isHotmail) {
                return document.querySelector('button[aria-label="Reply"][data-is-focusable="true"]');
            } else if (isGmail) {
                return findClosestNodeWithSelector(currentNode, 'span[role="link"]')?.querySelector('span[role="link"]');
            }
        }

        const ReplyWidget = ({ label }) => {
            const isDrafter = isReddit && document.getElementsByTagName('r-post-composer-form')?.length > 0
                || isLinkedIn && window.location.pathname.includes('article')
                || isLinkedIn && document.querySelector('.share-box')
                || isTwitter && !isDescendantOf(document.querySelector('.public-DraftEditor-content'), '[role="dialog"]')
                || isFacebook && document.querySelector('[role="dialog"]')?.querySelector('form[method="POST"]')

            const showReadButton = !isReddit && !isLinkedIn && !isTwitter && !isFacebook && !isProductHunt;

            return <div style={{
                display: 'flex', flexDirection: 'row', alignItems: 'center', alignSelf: 'center',
            }}>
                {
                    showReadButton &&
                    <div
                        style={{
                            display: 'flex', flexDirection: 'row', alignItems: 'center', alignSelf: 'center',
                            backgroundColor: 'dodgerblue', color: 'white', marginLeft: 8, columnGap: 8,
                            height: 32, borderRadius: 16, paddingLeft: 8, paddingRight: 8,
                            cursor: 'pointer', whiteSpace: 'nowrap'
                        }}
                        tabIndex={0}
                        onClick={() => {
                            window.postMessage({ type: 'launch_email_read' }, '*');
                        }}
                    >
                        <img width={20} height={20} style={{ margin: 0 }} src={imagePath} draggable={false} /> {label.read}
                    </div>
                }
                {
                    !isDrafter &&
                    <div
                        style={{
                            display: 'flex', flexDirection: 'row', alignItems: 'center', alignSelf: 'center',
                            backgroundColor: 'dodgerblue', color: 'white', marginLeft: !isLinkedIn ? 8 : undefined, marginRight: isLinkedIn ? 8 : undefined, columnGap: 8,
                            height: isLinkedIn ? 28 : 32, borderRadius: 16, paddingLeft: 8, paddingRight: 8,
                            fontSize: isLinkedIn ? 14 : undefined,
                            cursor: 'pointer', whiteSpace: 'nowrap'
                        }}
                        tabIndex={0}
                        onClick={(event) => {
                            event.preventDefault();
                            event.stopPropagation();
                            const replyDom = getOrignalReplyButtonDom(event.currentTarget);
                            if (replyDom) {
                                const clickEvent = new MouseEvent('click', {
                                    bubbles: true,
                                    cancelable: true,
                                    view: window,
                                });

                                replyDom.dispatchEvent(clickEvent);
                            }

                            window.postMessage({ type: 'launch_reply' }, '*');
                        }}
                    >
                        <img width={20} height={20} style={{ margin: 0 }} src={imagePath} draggable={false} /> {label.reply}
                    </div>
                }
                {
                    isDrafter &&
                    <div
                        style={{
                            display: 'flex', flexDirection: 'row', alignItems: 'center', alignSelf: 'center',
                            backgroundColor: 'dodgerblue', color: 'white', marginLeft: 10, columnGap: 8,
                            height: 32, borderRadius: 16, paddingLeft: 8, paddingRight: 10,
                            cursor: 'pointer', whiteSpace: 'nowrap'
                        }}
                        tabIndex={0}
                        onClick={(event) => {
                            event.preventDefault();
                            event.stopPropagation();

                            window.postMessage({ type: 'launch_ai_composer' }, '*');
                        }}
                    >
                        <img width={20} height={20} style={{ margin: 0 }} src={imagePath} draggable={false} /> {label.draft}
                    </div>
                }
            </div>
        }


        return {
            replyElementId,
            ReplyWidget,
            getHookDomForReply,
            getTargetEditorForReply,
            getMessageBodyDom,
        }
    }
)

export { useReply, ReplyProvider }