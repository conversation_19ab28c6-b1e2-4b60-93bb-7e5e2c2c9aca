import { openOptionPage } from "@/common/browser";
import { CLOSE_WIDGET_DIALOG } from "@/common/constants/actionTypes";
import { useSettings } from "@/common/store/settings";
import { Modal, Input, Radio, Space } from "antd";
import { getShadowDOMModalContainer } from '@/common/shadow-dom-utils';
import { useEffect, useState } from "react";
import { useIntl } from "react-intl";
import { useDispatch, useSelector } from "react-redux";

export const isWidgetClosed = (widget, settings, closeWidgetModalState) => {
    // console.log('is wdiget closed.......', widget, closeWidgetModalState, settings)
    if (!settings) return false;

    if (closeWidgetModalState?.widget === widget && closeWidgetModalState?.data?.range == 'this_visit') {
        return true
    }

    let range = settings?.widget_disabled_range[widget]?.range;

    if (range === 'globally' || settings?.widget_disabled_range[widget]?.pages?.includes(window.location.hostname)) {
        return true;
    }

    return false;
}

const CloseWidgetModal = ({ }) => {
    const modalState = useSelector(state => state.uiState.closeWidgetDialog) || {};
    const dispatch = useDispatch();
    const intl = useIntl();
    const { isModalOpen, widget, options = ['this_visit', 'this_page', 'globally'] } = modalState;
    const { loading, settings = { widget_disabled_range: {} }, setSettings } = useSettings()
    const [value, setValue] = useState();
    const { hostname } = window.location;

    useEffect(() => {
        let range = settings?.widget_disabled_range[widget]?.range || 'this_visit';
        if (range === 'this_page' && !settings?.widget_disabled_range[widget]?.pages?.includes(hostname)) {
            range = 'this_visit'
        }
        setValue(range)
    }, [isModalOpen])

    const onChange = (e) => {
        setValue(e.target.value);
    };

    const closeModal = (confirmed) => {
        dispatch({
            type: CLOSE_WIDGET_DIALOG,
            value: {
                ...modalState,
                isModalOpen: false,
                data: confirmed
            }
        })
    }

    const handleOk = () => {
        if (value !== 'this_visit') {
            let widget_disabled_range = {
                ...settings.widget_disabled_range,
            }
            widget_disabled_range[widget] = {
                ...widget_disabled_range[widget],
                range: value
            };

            if (value === 'this_page') {
                if (!widget_disabled_range[widget].pages) {
                    widget_disabled_range[widget].pages = [];
                }

                if (!widget_disabled_range[widget].pages.includes(hostname)) {
                    widget_disabled_range[widget].pages.push(hostname);
                }
            }

            // console.log('update settings..........', settings, widget_disabled_range)

            setSettings({
                ...settings,
                widget_disabled_range
            })
        }

        closeModal({ range: value });
    };

    const handleCancel = () => {
        closeModal(null);
    };

    return (
        <Modal
            title={intl.formatMessage({id: 'disable_confirm'})}
            width={360}
            centered
            open={isModalOpen}
            onOk={handleOk}
            onCancel={handleCancel}
            okText={intl.formatMessage({id: 'confirm'})}
            getContainer={getShadowDOMModalContainer}
        >
            <div style={{
                paddingLeft: 10,
            }}>
                <Radio.Group onChange={onChange} value={value || 'this_visit'}>
                    <Space direction="vertical">
                        {
                            options.map((option) => {
                                return <Radio value={option}>{intl.formatMessage({ id: `disable_${option}` })}</Radio>
                            })
                        }
                    </Space>
                </Radio.Group>

                <div style={{
                    color: 'gray',
                    display: 'flex',
                    flexDirection: 'row',
                    columnGap: 4,
                    marginTop: 16
                }}>
                    * {intl.formatMessage({id: 'can_reopen'})}
                    <div style={{ color: 'dodgerblue', cursor: 'pointer' }}
                        onClick={openOptionPage}
                    >
                        {intl.formatMessage({id: 'settings'})}
                    </div>
                </div>
            </div>
        </Modal>
    );
}

export default CloseWidgetModal;