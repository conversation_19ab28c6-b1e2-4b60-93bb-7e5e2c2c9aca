import { getContentDom } from "@/content/utils/content-dom";
import { Flowchart } from "@styled-icons/fluentui-system-regular";
import { Tooltip } from "antd";
import { useEffect, useState } from "react";
import { ThreeDots } from "react-loader-spinner";
import { createContainer } from "unstated-next";
import Browser from "webextension-polyfill";
import { getCurrentProduct, ProductFeatures } from "@/common/product-features";

const product = getCurrentProduct()
const imagePath = Browser.runtime.getURL(`assets/${product}-icon.png`);

const { useContainer: useSummary, Provider: SummaryProvider } = createContainer(
    () => {
        // const summaryElementId = ProductFeatures.getProductId('summary-widget');
        const summaryElementId = 'funblocks-video-summary-widget';

        const getHookDomForSummary = () => {
            return document.getElementById('secondary-inner')?.parentNode
        }

        const SummaryWidget = ({ video_assistant_label, summary_label, launch_ril_tooltip, launch_mindmap_tooltip, launch_summary_tooltip, loading, err_msg }) => {
            const [hovered, setHovered] = useState();
            const [errMsg, setErrMsg] = useState();

            useEffect(() => {
                if (err_msg) {
                    setErrMsg(err_msg);
                    setTimeout(() => setErrMsg(null), 5000);
                }
            }, [err_msg])

            return <div style={{
                display: 'flex', flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between',
                height: 56, borderRadius: 8, paddingLeft: 12, paddingRight: 12,
                marginBottom: 10,
                backgroundColor: ProductFeatures.isMindMap() && '#f3f3f3' || 'dodgerblue',
                color: ProductFeatures.isMindMap() && 'black' || 'white',
            }}>
                <Tooltip placement="bottom" title={launch_ril_tooltip} zIndex={10000}>
                    <div
                        style={{
                            display: 'flex', flexDirection: 'row', alignItems: 'center', alignSelf: 'center',
                            padding: 6,
                            backgroundColor: !loading && hovered?.id == 'video_ai' ? (ProductFeatures.isMindMap() && 'lightskyblue' || 'royalblue') : (ProductFeatures.isMindMap() && '#f3f3f3' || 'dodgerblue'),
                            color: ProductFeatures.isMindMap() && 'black' || 'white',
                            columnGap: 6,
                            cursor: 'pointer', whiteSpace: 'nowrap',
                            fontSize: 15, fontWeight: 'bold',
                            borderRadius: 6,
                        }}
                        tabIndex={0}
                        onClick={() => {
                            window.postMessage({ type: 'launch_ril' }, '*');
                        }}
                        onMouseEnter={() => {
                            setHovered({ id: 'video_ai' })
                        }}
                        onMouseLeave={() => {
                            setHovered(null)
                        }}
                    >
                        <img width={24} height={24} src={imagePath} draggable={false} />
                        {video_assistant_label}
                        {
                            loading?.operation == 'launch_ril' &&
                            <ThreeDots
                                height="8"
                                width="30"
                                radius="9"
                                color='white'
                                ariaLabel='three-dots-loading'
                            />
                        }
                    </div>
                </Tooltip>

                <div style={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'flex-end'
                }}>
                    <div style={{
                        display: 'flex',
                        flexDirection: 'row',
                        alignItems: 'center',
                        columnGap: 6
                    }}>
                        <Tooltip placement="bottom" title={launch_mindmap_tooltip} zIndex={10000}>
                            <div
                                style={{
                                    display: 'flex', flexDirection: 'row', alignItems: 'center', justifyContent: 'center',
                                    padding: 5,
                                    backgroundColor: !loading && hovered?.id == 'video_mindmap' ? 'royalblue' : 'darkcyan',
                                    color: 'white',
                                    columnGap: 6,
                                    cursor: 'pointer', whiteSpace: 'nowrap',
                                    // border: '1px solid gray',
                                    borderRadius: '5px',
                                    fontSize: 14,
                                    width: 'fit-content',
                                    minWidth: 60,
                                    border: '1px solid #ccc'
                                }}
                                tabIndex={0}
                                onClick={() => {
                                    window.postMessage({ type: 'launch_video_mindmap' }, '*');
                                }}
                                onMouseEnter={() => {
                                    setHovered({ id: 'video_mindmap' })
                                }}
                                onMouseLeave={() => {
                                    setHovered(null)
                                }}
                            >
                                <Flowchart size={20} />
                                {
                                    loading?.operation == 'launch_video_mindmap' &&
                                    <ThreeDots
                                        height="8"
                                        width="30"
                                        radius="9"
                                        color='white'
                                        ariaLabel='three-dots-loading'
                                    />
                                }
                            </div>
                        </Tooltip>
                        {ProductFeatures.isYoutubeSummaryButtonEnabled() && (
                            <Tooltip placement="bottom" title={launch_summary_tooltip} zIndex={10000}>
                                <div
                                    style={{
                                        display: 'flex', flexDirection: 'row', alignItems: 'center',
                                        padding: 6,
                                        backgroundColor: !loading && hovered?.id == 'video_summarize' ? 'royalblue' : 'rgba(0,0,0,0.4)',
                                        color: 'white',
                                        columnGap: 6,
                                        cursor: 'pointer', whiteSpace: 'nowrap',
                                        border: '1px solid gray',
                                        borderRadius: '5px',
                                        fontSize: 14,
                                        width: 'fit-content'
                                    }}
                                    tabIndex={0}
                                    onClick={() => {
                                        window.postMessage({ type: 'launch_summary' }, '*');
                                    }}
                                    onMouseEnter={() => {
                                        setHovered({ id: 'video_summarize' })
                                    }}
                                    onMouseLeave={() => {
                                        setHovered(null)
                                    }}
                                >
                                    {summary_label}
                                    {
                                        loading?.operation == 'launch_summary' &&
                                        <ThreeDots
                                            height="8"
                                            width="30"
                                            radius="9"
                                            color='white'
                                            ariaLabel='three-dots-loading'
                                        />
                                    }
                                </div>
                            </Tooltip>
                        )}
                    </div>
                    {
                        errMsg &&
                        <div><span style={{ color: 'gold', fontSize: 14 }}>{errMsg}</span></div>
                    }
                </div>
            </div>
        }

        return {
            summaryElementId,
            SummaryWidget,
            getHookDomForSummary,
        }
    }
)

export { useSummary, SummaryProvider }