import { Avatar, Button, Input, Tag } from 'antd'
import {
  forwardRef,
  PropsWithChildren,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from 'react'
import cx from 'classnames'
import { Logo } from '@/components/icon'
import i18next from 'i18next'
import { useView, action_bar_widget_id } from '../../store/view'
import AIForm from './ai-form'
import { useSelectionManager } from '../../store/selection'
import { Check, Close, ContentCopy, Translate } from '@styled-icons/material'
import { Pencil } from '@styled-icons/bootstrap/Pencil'
import { MagicWand } from '@styled-icons/boxicons-solid/MagicWand'
import { TextGrammarWand } from '@styled-icons/fluentui-system-regular/TextGrammarWand'
import { SaveCopy } from '@styled-icons/fluentui-system-filled/SaveCopy'
import { Done } from '@styled-icons/material/Done'
import { Book } from '@styled-icons/boxicons-regular/Book'
import { Flowchart } from '@styled-icons/fluentui-system-regular/Flowchart'
import { Translate } from '@styled-icons/bootstrap/Translate'
import { Save } from '@styled-icons/boxicons-regular/Save'
import { EmojiSparkle } from '@styled-icons/fluentui-system-regular/EmojiSparkle'
import { Tooltip } from 'antd'
import { useIntl } from 'react-intl'
import { useSettings } from '@/common/store/settings'
import { extractWebContent } from '@/common/article_extractor'
import { useDispatch, useSelector } from 'react-redux'
import {
  addNote,
  detectLanguage,
  getUserInfo,
} from '@/common/actions/ticketAction'
import { CLOSE_WIDGET_DIALOG } from '@/common/constants/actionTypes'
import { isWidgetClosed } from '../widgets/close-widget-modal'
import ConfirmMessage from '../confirm_message'
import { EventName } from '@/common/event-name'
import browser from 'webextension-polyfill'
import { isGoogleDocs } from '@/content/utils/content-dom'
import { funblocks_domain } from '@/common/serverAPIUtil'
import { Microphone } from '@styled-icons/boxicons-regular/Microphone'
import { Selector } from '@/common/selector'
import { Scales } from '@styled-icons/fluentui-system-regular'

export const Content: React.FC<PropsWithChildren> = ({
  close_widget_modal_state,
}) => {
  return <CenterContent close_widget_modal_state={close_widget_modal_state} />
}

const CenterContent = forwardRef<HTMLDivElement>((_, ref) => {
  const intl = useIntl()
  const {
    viewStatus,
    goToInputPage,
    hide,
    reset,
    objType,
    action,
    sub_item_action,
    drafter,
    generator,
  } = useView()
  const selection = useSelectionManager()
  const dispatch = useDispatch()
  const selectedText = selection.getSelectedContent()?.text
  const isEditable = selection.getSelectedContent()?.isEditable || isGoogleDocs
  const loginUser = useSelector((state) => state.loginIn && state.loginIn.user)

  const { settings = {} } = useSettings()
  const respond_lng =
    settings?.ai_respond_lng == 'cn' ? 'zh' : settings?.ai_respond_lng

  const app_config = useSelector((state) => state.uiState.app_config)
  const assistant_items = app_config?.assistant_items
  const translate_item = assistant_items?.find(
    (item) => item.action == 'translate'
  )
  const change_tone_item = assistant_items?.find(
    (item) => item.action == 'tone'
  )

  const close_widget_modal_state = _.close_widget_modal_state
  const [confirmMessage, setConfirmMessage] = useState()
  const [toast, setToast] = useState()

  // useEffect(() => {
  //   dispatch(getUserInfo({ locale: settings.lang }))

  //   if (!selection.getPageLanguage()) {
  //     const article = extractWebContent()
  //     dispatch(
  //       detectLanguage({ text: article.textContent }, (lng) => {
  //         selection.setPageLanguage(lng)
  //       })
  //     )
  //   }
  // }, [])

  const handleClickIcon = useCallback(() => {
    goToInputPage({})
  }, [goToInputPage])

  const showToast = useCallback((message) => {
    setToast(message)
    setTimeout(() => {
      setToast(null)
      hide()
    }, 3000)
  }, [])

  const saveToMemo = useCallback(
    async (selectedText) => {
      if (!loginUser?._id) {
        return setConfirmMessage({
          content: intl.formatMessage({ id: 'feature_for_members' }),
          okText: intl.formatMessage({ id: 'settings' }),
          onConfirm: () => {
            browser.runtime.sendMessage({
              type: EventName.showOptions,
            })

            setConfirmMessage({
              content: intl.formatMessage({ id: 'confirm_logon' }),
              onConfirm: () =>
                dispatch(
                  getUserInfo(
                    {},
                    () => {
                      setConfirmMessage(null)
                      saveToMemo(selectedText)
                    },
                    () => {
                      setConfirmMessage(null)
                      saveToMemo(selectedText)
                    }
                  )
                ),
            })
          },
        })
      }

      let article = await extractWebContent()
      const lines = selectedText?.trim().split('\n')
      const title = article?.title
      let blocks = [
        {
          type: 'blockquote',
          children: lines.map((line) => {
            return {
              type: 'p',
              children: [
                {
                  text: line,
                },
              ],
            }
          }),
        },
      ]

      let data = {
        title,
        content: blocks,
        url: window.location.href,
        // article_title: article?.title,
        article_content: article?.content,
      }

      dispatch(
        addNote(
          {
            data,
          },
          () => {
            showToast(intl.formatMessage({ id: 'saved_to_memo' }))
          },
          'widget'
        )
      )
    },
    [selectedText, loginUser]
  )

  const openSubMenu = useCallback((item) => {}, [])

  const doneIcon = <Done size={16} />

  if (confirmMessage) {
    return (
      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          borderRadius: '6px',
          width: '300px',
          height: '140px',
          padding: '10px',
          boxShadow: '0px 4px 6px rgba(0, 0, 0, 0.2)',
          transition: 'box-shadow 0.3s ease-in-out',
          backgroundColor: '#fafafa',
          color: '#333',
        }}
      >
        <ConfirmMessage
          content={confirmMessage.content}
          okText={confirmMessage.okText}
          onCancel={() => setConfirmMessage(null)}
          onConfirm={confirmMessage.onConfirm}
          style={{
            padding: 10,
          }}
        />
      </div>
    )
  }

  if (toast) {
    return (
      <div
        style={{
          display: 'flex',
          flexDirection: 'row',
          columnGap: 4,
          alignItems: 'center',
          justifyContent: 'center',
          borderRadius: '5px',
          padding: '6px',
          paddingLeft: '12px',
          paddingRight: '12px',
          boxShadow: '0px 4px 6px rgba(0, 0, 0, 0.3)',
          transition: 'box-shadow 0.3s ease-in-out',
          backgroundColor: '#fafafa',
          color: '#333',
          fontSize: 14,
        }}
      >
        <div>{toast}</div>
        <Check size={18} color="green" />
      </div>
    )
  }

  if (viewStatus === 'icon') {
    const e = selection.getTriggerEvent()
    if (
      settings &&
      ((settings.contextual_toolbar_always != 'shortcuts' &&
        isWidgetClosed(
          action_bar_widget_id,
          settings,
          close_widget_modal_state
        )) ||
        (settings.contextual_toolbar_always == 'shortcuts' &&
          e.key?.toLowerCase() !=
            settings.contextual_toolbar_hotkey?.toLowerCase()))
    ) {
      reset()
      return <></>
    }

    if (e.type == 'keyup') {
      e.stopPropagation()
      e.preventDefault()
    }

    return (
      <div
        // className='!shadow-sm'
        style={{
          display: 'flex',
          flexDirection: 'row',
          backgroundColor: 'dodgerblue',
          color: 'white',
          alignItems: 'center',
          justifyContent: 'center',
          borderRadius: '6px',
          padding: '1px',
          boxShadow: '0px 4px 6px rgba(0, 0, 0, 0.2)',
          transition: 'box-shadow 0.3s ease-in-out',
        }}
      >
        {selectedText && settings.balloon_copy_enabled === 'true' && (
          <Tooltip title={intl.formatMessage({ id: 'copy' })}>
            <div
              className="balloon_icon_button"
              onClick={() => {
                navigator.clipboard.writeText(selectedText)
                showToast(intl.formatMessage({ id: 'copied' }))
              }}
            >
              <ContentCopy size={17} />
            </div>
          </Tooltip>
        )}
        {selectedText && isEditable && (
          <>
            <div
              className="balloon_icon_button"
              style={styles.button}
              onClick={() =>
                goToInputPage({ objType: 'markdown', action: 'optimize' })
              }
            >
              <TextGrammarWand size={19} />
              {intl.formatMessage({ id: 'optimize' })}
            </div>
            <div
              className="balloon_icon_button"
              style={styles.button}
              onClick={() =>
                goToInputPage({ objType: 'markdown', action: 'continue' })
              }
            >
              <Pencil size={15} />
              {intl.formatMessage({ id: 'continue' })}
            </div>

            {change_tone_item && (
              <Selector
                options={change_tone_item.sub_items}
                triggerElement={
                  <div
                    className="balloon_icon_button"
                    style={styles.button}
                    onClick={openSubMenu}
                  >
                    <Microphone size={17} />
                    {intl.formatMessage({ id: 'change_tone' })}
                  </div>
                }
                showOpenIndicator={true}
                onChange={(value) => {
                  goToInputPage({
                    objType: 'markdown',
                    action: 'tone',
                    sub_item_action: value,
                  })
                }}
              />
            )}

            {translate_item && (
              <Selector
                options={translate_item.sub_items}
                triggerElement={
                  <div
                    className="balloon_icon_button"
                    style={styles.button}
                    onClick={openSubMenu}
                  >
                    <Translate size={17} />
                    {intl.formatMessage({ id: 'translate' })}
                  </div>
                }
                showOpenIndicator={true}
                onChange={(value) => {
                  goToInputPage({
                    objType: 'markdown',
                    action: 'translate',
                    sub_item_action: value,
                  })
                }}
              />
            )}
          </>
        )}
        {selectedText && !isEditable && (
          <>
            <Tooltip title={intl.formatMessage({ id: 'save_to_memo' })}>
              <div
                className="balloon_icon_button"
                onClick={() => saveToMemo(selectedText)}
              >
                <Save size={20} />
              </div>
            </Tooltip>
            <div
              className="balloon_icon_button"
              style={styles.button}
              onClick={() =>
                goToInputPage({ objType: 'markdown', action: 'explain' })
              }
            >
              <Book size={17} />
              {intl.formatMessage({ id: 'explain' })}
            </div>

            {/* {respond_lng && respond_lng == selection?.getPageLanguage() && ( */}
            <div
              className="balloon_icon_button"
              style={styles.button}
              onClick={() =>
                goToInputPage({
                  objType: 'markdown',
                  action: 'critial_analysis',
                })
              }
            >
              <Scales size={18} />
              {intl.formatMessage({ id: 'critical_analysis' })}
            </div>
            {/* )} */}

            {/* {(!respond_lng || respond_lng != selection?.getPageLanguage()) && ( */}
            <Tooltip title={intl.formatMessage({ id: 'translate' })}>
              <div
                className="balloon_icon_button"
                style={styles.button}
                onClick={() =>
                  goToInputPage({ objType: 'markdown', action: 'translate' })
                }
              >
                <Translate size={17} />
                {/* {intl.formatMessage({ id: 'translate' })} */}
              </div>
            </Tooltip>
            {/* )} */}

            <Tooltip title={intl.formatMessage({ id: 'breakdown_topic' })}>
              <div
                className="balloon_icon_button"
                style={styles.button}
                onClick={() => {
                  // window.open(
                  //   `https://app.${funblocks_domain}/#/aiflow?queryType=breakdown&action=breakdown&userInput=${selectedText}`,
                  //   '_blank'
                  // )

                  // console.log('url.........', `http://localhost:3000/#/aiflow?queryType=breakdown&action=breakdown&userInput=${selectedText}`)
                  // window.open(
                  //   `http://localhost:3000/#/aiflow?queryType=breakdown&action=breakdown&userInput=${selectedText}`,
                  //   '_blank'
                  // )

                  selection.setSelectedContent({
                    text: selectedText,
                    type: 'markdown',
                  })
                  goToInputPage({ objType: 'flow', action: 'breakdown' })
                }}
              >
                <Flowchart size={19} />
                {/* {intl.formatMessage({ id: 'translate' })} */}
              </div>
            </Tooltip>
          </>
        )}
        <Tooltip title={intl.formatMessage({ id: 'ask_ai' })}>
          <div className="balloon_icon_button" onClick={handleClickIcon}>
            <Logo />
          </div>
        </Tooltip>
        <div
          className="balloon_icon_button"
          onClick={() => {
            hide()
            dispatch({
              type: CLOSE_WIDGET_DIALOG,
              value: {
                isModalOpen: true,
                widget: action_bar_widget_id,
                options: ['this_visit', 'this_page'],
              },
            })
          }}
        >
          <Close color="#ddd" size={18} />
        </div>
      </div>
    )
  }

  // console.log('will open ai form.........', objType, action)
  return (
    <AIForm
      hide={hide}
      objType={objType}
      init_action={action}
      sub_item_action={sub_item_action}
      drafter={drafter}
      selected_content={selection.getSelectedContentForAI()}
      generator={generator}
    />
  )
})

const styles = {
  button: {
    width: 'fit-content',
    fontSize: 14,
    paddingLeft: 6,
    paddingRight: 6,
    display: 'flex',
    flexDirection: 'row',
    columnGap: 3,
  },
}
