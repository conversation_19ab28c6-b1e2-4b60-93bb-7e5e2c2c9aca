import { Selector } from '@/common/selector';
import { useEffect, useMemo, useState } from 'react';
import { useIntl } from 'react-intl';

export const Mental_Models = {
  flow_decision_analysis: [{
    id: 'none'
  }, {
    id: 'pros_cons'
  }, {
    id: 'swot_analysis'
  }, {
    id: 'first_principle'
  }, {
    id: 'six_thinking_hats'
  }, {
    id: 'cost_benefit_analysis'
  }, {
    id: 'decision_tree'
  }, {
    id: 'decision_matrix'
  }, {
    id: 'casual_chain'
  }, {
    id: 'systems_thinking'
  }, {
    id: 'second_order_thinking'
  }, {
    id: 'inversion_thinking'
  }, {
    id: 'rephrazing'
  }, {
    id: 'scientific_method'
  }, {
    id: 'changing_perspectives'
  }, {
    id: 'reverse_thinking'
  }, {
    id: 'other'
  }],

  flow_brainstorming: [{
    id: 'none'
  }, {
    id: 'swot_analysis'
  }, {
    id: 'business_model_canvas'
  }, {
    id: 'eisenhower_matrix'
  }, {
    id: 'first_principle'
  }, {
    id: 'fivew1h_method'
  }, {
    id: 'scamper_method'
  }, {
    id: 'six_thinking_hats'
  }, {
    id: 'pyramid_principle'
  }, {
    id: 'systems_thinking'
  }, {
    id: 'dialectical_thinking'
  }, {
    id: 'probabilistic_thinking',
  }, {
    id: 'steep_analysis'
  }, {
    id: 'five_forces'
  }, {
    id: 'four_p'
  }, {
    id: 'triz'
  }, {
    id: 'rephrazing'
  }, {
    id: 'scientific_method'
  }, {
    id: 'learning_pyramid',
  }, {
    id: 'occams_razor'
  }, {
    id: 'changing_perspectives'
  }, {
    id: 'reverse_thinking'
  }, {
    id: 'role_playing'
  }, {
    id: 'mece_principle'
  }, {
    id: 'value_proposition_canvas'
  }, {
    id: 'other'
  }]
}

const MentalModelSelector = ({ app, onSelect, value, onInput }) => {
  const intl = useIntl();

  const [mentalModels, setMentalModels] = useState();

  useEffect(() => {
    setMentalModels(Mental_Models[app]?.
      map(mm => {
        return {
          value: mm.id,
          label: intl.formatMessage({ id: mm.id })
        }
      }))
  }, [app, intl])

  return (
    <div style={{
      display: 'flex',
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'flex-start',
    }}>
      <Selector
        options={mentalModels}
        value={value?.value || ''}
        onChange={(value) => {
          onSelect(value ? mentalModels.find(type => type.value === value) : null);
        }}
        inputStyle={{
          borderRadius: 4,
        }}
      />
      
      {
        value?.value === 'other' &&
        <input
          type='text'
          value={value?.inputValue || ''}
          onChange={onInput}
          placeholder={intl.formatMessage({ id: 'enter_mental_model' })}
          className='fill-available'
          style={{
            border: '1px solid rgb(216, 216, 216)',
            boxShadow: 'none',
            width: 150,
            zIndex: 1,
            autoFocus: true,
            backgroundColor: 'white',
            padding: 4,
            borderRadius: 4,
            marginLeft: 4,
            outline: 'none',
            color: '#333',
          }}
        />
      }
    </div>
  )
}

export default MentalModelSelector
