import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useIntl } from 'react-intl';
import {
  EmailIcon,
  EmailShareButton,
  FacebookIcon,
  FacebookMessengerIcon,
  FacebookMessengerShareButton,
  FacebookShareButton,
  LineIcon,
  LineShareButton,
  LinkedinIcon,
  LinkedinShareButton,
  RedditIcon,
  RedditShareButton,
  TwitterShareButton,
  WhatsappIcon,
  WhatsappShareButton,
  TwitterIcon
} from 'react-share';
import { Checkbox, Popover, Tooltip } from 'antd'
import { ThreeDots } from 'react-loader-spinner';
import { useDispatch, useSelector } from 'react-redux';
import { getServingProducts } from '@/common/actions/ticketAction';
import { Close } from '@styled-icons/material';
import { Link45deg } from '@styled-icons/bootstrap/Link45deg'
import { truncateString } from '@/common/utils';
import { getCurrentBrowserFingerPrint } from "@rajesh896/broprint.js";

export const SharePopover = ({ visible, toggleShare, handleClose, children, shareData, beforeOnClick }) => {
  const intl = useIntl()
  const dispatch = useDispatch()
  const { uploading } = shareData || {}
  const [visibility, setVisibility] = useState('2')
  const loginUser = useSelector(state => state.loginIn && state.loginIn.user);
  const serving_products = useSelector(state => state.uiState.serving_products) || [];
  const [rewardable, setRewardable] = useState();
  const [btn_hovered, set_btn_hovered] = useState();
  const [fingerprint, set_fingerprint] = useState()
  const iframeRef = useRef();
  const [copied, setCopied] = useState();

  useEffect(() => {
    loginUser?._id && dispatch(getServingProducts({ services: ['aiplus'] }, null, 'share'));
  }, [loginUser])

  useEffect(() => {
    setRewardable(loginUser?._id && !serving_products?.find(p => p.name != 'free'));
  }, [loginUser, serving_products])

  useEffect(() => {
    getCurrentBrowserFingerPrint().then((fingerprint) => {
      set_fingerprint(fingerprint)
    })
  }, [])

  useEffect(() => {
    if (iframeRef?.current) {
      iframeRef.current.src = visible && !!shareData?.url && (shareData.url + '&raw=1&fp=' + fingerprint);
    }
  }, [visible, shareData?.url, iframeRef?.current, fingerprint])

  const ShareButton = <div
    className='hoverButton'
    style={{
      display: 'flex',
      flexDirection: 'row',
      justifyContent: 'flex-end',
      alignItems: 'center',
      columnGap: 6,
      whiteSpace: 'nowrap',
      cursor: 'pointer',
      paddingLeft: 14,
      paddingRight: 14,
      backgroundColor: uploading ? 'white' : undefined,
      color: uploading ? 'dodgerblue' : undefined,
    }}
    onClick={() => !uploading && toggleShare(visibility)}
    onMouseEnter={() => set_btn_hovered(true)}
  >
    {intl.formatMessage({ id: 'share' })}
    {uploading && <ThreeDots
      height="8"
      width="30"
      radius="9"
      color='dodgerblue'
      ariaLabel='three-dots-loading'
    />}
  </div>

  const ShareWidget = <div
    style={{
      display: 'flex',
      flexDirection: 'row',
      alignItems: 'center',
      columnGap: 4
    }}
    onMouseLeave={() => set_btn_hovered(false)}
  >
    {!rewardable && ShareButton}
    {
      rewardable &&
      <Tooltip
        title={intl.formatMessage({ id: 'share_aigc' })}
      >
        {ShareButton}
      </Tooltip>
    }
    {
      btn_hovered &&
      <Checkbox
        checked={visibility == '2'}
        onChange={(event) => {
          setVisibility(event.target.checked ? '2' : '1')
        }}
      >
        <div style={{ fontSize: 14, color: 'GrayText', display: 'flex', flexDirection: 'row', alignItems: 'center' }}>
          {intl.formatMessage({ id: 'share_to' })}
          <a href='https://app.funblocks.net/shares' target='_blank' style={{ textDecoration: 'none', fontSize: 14, color: 'deepskyblue' }}>&nbsp;{intl.formatMessage({ id: 'aigc_community' })}</a>
        </div>
      </Checkbox>
    }
    {
      !btn_hovered && !rewardable &&
      <span style={{ fontSize: 14, color: 'GrayText' }}>{intl.formatMessage({ id: 'share_aigc' })}</span>
    }
    {
      !btn_hovered && rewardable &&
      <span style={{ fontSize: 14, color: 'GrayText' }}>{intl.formatMessage({ id: 'share_aigc_reward_tooltip' })}</span>
    }
  </div>

  if (!shareData) {
    return ShareWidget;
  }

  const shareUrl = shareData.url;
  const shareContent = shareData.quoteText;

  return <Popover
    overlayInnerStyle={{ padding: 0 }}
    content={
      <div style={{ width: 500, position: 'relative', display: 'flex', flexDirection: 'column', justifyContent: 'flex-start' }}>
        <iframe ref={iframeRef} src={shareData.url + '&raw=1&fp=' + fingerprint} width="100%" height="400px" frameBorder="0"></iframe>
        <div style={{
          width: 16,
          height: 16,
          borderRadius: 20,
          position: 'absolute',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          right: -6,
          top: -8,
          color: 'white',
          backgroundColor: 'rgba(108,108,108, 0.6)',
          cursor: 'pointer',
          zIndex: 'inherit'
        }}
          onClick={() => {
            handleClose(false)
          }}
        >
          <Close size={12} />
        </div>
        <div className='fill-available'
          style={{
            display: 'flex',
            flexDirection: 'row',
            justifyContent: 'space-around',
            columnGap: 6,
            padding: 8,
            paddingBottom: 2,
            borderRadius: 5,
            boxShadow: '0px 4px 6px rgba(0, 0, 0, 0.2)',
            backgroundColor: 'rgba(242,242,242, 0.8)',
            position: 'absolute',
            bottom: 0,
            left: 0
          }}>
          <FacebookShareButton
            url={shareUrl}
            quote={shareContent}
            hashtag="FunBlocks_AI"
            beforeOnClick={beforeOnClick}
          >
            <FacebookIcon size={32} round />
          </FacebookShareButton>
          {/* <FacebookMessengerShareButton
          url={shareUrl}
          appId="521270401588372"
          className="Demo__some-network__share-button"
        >
          <FacebookMessengerIcon size={32} round />
        </FacebookMessengerShareButton> */}
          <TwitterShareButton
            url={shareUrl}
            title={truncateString(shareContent, 230)}
            hashtags={['FunBlocks_AI', 'AIGC']}
            className="Demo__some-network__share-button"
          >
            <TwitterIcon size={32} round />
          </TwitterShareButton>
          <WhatsappShareButton
            url={shareUrl}
            title={shareContent}

            separator=":: "
            className="Demo__some-network__share-button"
          >
            <WhatsappIcon size={32} round />
          </WhatsappShareButton>

          <LineShareButton url={shareUrl} title={shareContent} className="Demo__some-network__share-button">
            <LineIcon size={32} round />
          </LineShareButton>

          <LinkedinShareButton
            url={shareUrl}
            title={shareData.title}
            summary={shareContent}
            source='https://funblocks.net'
            className="Demo__some-network__share-button"
            beforeOnClick={beforeOnClick}
          >
            <LinkedinIcon size={32} round />
          </LinkedinShareButton>

          <RedditShareButton
            url={shareUrl}
            title={shareContent}
            windowWidth={660}
            windowHeight={460}
            className="Demo__some-network__share-button"
          >
            <RedditIcon size={32} round />
          </RedditShareButton>

          <EmailShareButton
            url={shareUrl}
            subject={shareData.title}
            body={shareContent}
            className="Demo__some-network__share-button"
          >
            <EmailIcon size={32} round />
          </EmailShareButton>
          <Tooltip
            title={intl.formatMessage({ id: copied? 'copied' : 'copy_link' })}
          >
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                width: 31,
                height: 31,
                borderRadius: 31,
                backgroundColor: 'rgb(127,127,127)',
                color: 'white',
                cursor: 'pointer'
              }}

              onClick={() => { 
                navigator.clipboard.writeText(shareUrl);
                setCopied(true);
                setTimeout(() => setCopied(false), 3000);
              }}
            >
              <Link45deg size={22} />
            </div>
          </Tooltip>
        </div>
      </div>
    }
    trigger="click"
    placement="top"
    arrow={false}
    open={visible}
  >
    {ShareWidget}
  </Popover>
};

