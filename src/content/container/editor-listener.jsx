import { useCallback, useEffect, useState } from "react";
import { useView } from "./store/view";
import { useSelectionManager } from "./store/selection";
import { useSettings } from "@/common/store/settings";
import { useIntl } from "react-intl";
import { onKeyPressEvent } from "../utils/keyboard/key-event-listener";
import { debounce } from 'lodash-es'
import { ProductFeatures } from "@/common/product-features";

const EditorListener = () => {
  const { openWriterAssistantForEditor } = useView();
  const selectionManager = useSelectionManager();
  const { settings } = useSettings();
  const intl = useIntl();

  const triggeredByKey = (keyEvent, focus_at, trigger) => {
    // selectionManager.select_from_start(keyEvent.target, trigger);
    // openWriterAssistant();
    openWriterAssistantForEditor(keyEvent.target, trigger)
  }

  const handleKeyPress = (e) => settings?.trigger && settings?.trigger != 'none' && onKeyPressEvent(e, settings.trigger, triggeredByKey)

  useEffect(() => {
    if(!ProductFeatures.isWriteAssistantEnabled()) {
      return;
    }

    if (settings?.trigger && settings.trigger != 'none') {


      var editors = document.querySelectorAll('textarea');

      editors.forEach(editor => {
        editor.placeholder = intl.formatMessage({ id: 'injected_ai_trigger_placeholder' }, { trigger: settings.trigger })
      });
    }

    // selectionManager.onKeyPressed(triggeredByKey)

    // let textboxes = document.querySelectorAll('[role="textbox"]')
    // for (let i = 0; i < textboxes.length; i++) {
    //   let ele = textboxes[i]
    //   let ele_id =
    //     (ele.id || ele.nodeName) + ele.className?.replaceAll(' ', '')
    //   // console.log('is registered...........', ele_id, ele, this.keyEventRegisteredDoms[ele_id])
    //   // if (!this.keyEventRegisteredDoms[ele_id]) {
    //   ele.addEventListener('keyup', handleKeyPress)
    //   // this.keyEventRegisteredDoms[ele_id] = true
    //   // }
    // }

    document.addEventListener('keyup', handleKeyPress);
    return () => {
      // let textboxes = document.querySelectorAll('[role="textbox"]')
      // for (let i = 0; i < textboxes.length; i++) {
      //   let ele = textboxes[i]
      //   // let ele_id =
      //   //   (ele.id || ele.nodeName) + ele.className?.replaceAll(' ', '')
      //   ele.removeEventListener('keyup', handleKeyPress)
      // }

      document.removeEventListener('keyup', handleKeyPress);
    };
  }, [settings?.trigger])

  return null;
}

export default EditorListener