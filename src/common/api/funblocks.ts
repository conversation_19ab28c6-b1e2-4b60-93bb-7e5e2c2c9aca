import useSWR from 'swr'
import browser from 'webextension-polyfill'
import { EventName } from '../event-name'
import { funblocks_domain } from '../serverAPIUtil'

export const useUser = () => {
  return useSWR(['user'], async () => {
    const result = await fetch(`https://service.${funblocks_domain}/users/info`)
    const value = (await result.json()) as {
      data: {
        user: {
          email: string
        }
      }
    }

    return value
  })
}

let funblocks_token = ''
;(async function _getToken() {
  const setToken = async () => {
    const result = await browser.runtime.sendMessage({
      type: EventName.getToken,
    })
    funblocks_token = decodeURI(result)
  }

  setToken()

  setInterval(setToken, 5000)
})()

export const getToken = () => funblocks_token
