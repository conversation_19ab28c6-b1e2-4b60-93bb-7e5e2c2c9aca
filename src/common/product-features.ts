// Product feature detection utilities

// Get current product from environment or default to funblocks
export function getCurrentProduct(): string {
  // @ts-ignore - process.env.PRODUCT is defined at build time
  return process.env.PRODUCT || 'funblocks'
}

// Generate product-specific ID to avoid conflicts between different products
export function getProductId(baseId: string): string {
  const product = getCurrentProduct()
  return `${product}-${baseId}`
}

// Generate product-specific class name
export function getProductClass(baseClass: string): string {
  const product = getCurrentProduct()
  return `${product}-${baseClass}`
}

// Product feature configurations
const productFeatures = {
  funblocks: {
    sidebarMenu: {
      ai_flow_question: true,
      ai_flow_explore: true,
      assistant_with_page_context: true,
      assistant_without_page_context: true,
      assistant_summarize: true,
      quick_settings: true,
      screen_capture: true,
      all_features: true,
    },
    widgets: {
      youtube: {
        enabled: true,
        showSummaryButton: true,
      },
      reply: true,
      aiWriter: true,
      codesExplain: true,
      allWidgets: true,
    },
    contextualMenu: true,
    write_assistant: true,
    popup: true,
    options: true,
  },
  mindmap: {
    sidebarMenu: {
      ai_flow_question: true,
      ai_flow_explore: true,
      assistant_with_page_context: true,
      assistant_without_page_context: false,
      assistant_summarize: true,
      quick_settings: false,
      screen_capture: false,
      all_features: false,
    },
    widgets: {
      youtube: {
        enabled: true,
        showSummaryButton: false,
      },
      reply: false,
      aiWriter: false,
      codesExplain: false,
      allWidgets: false,
    },
    contextualMenu: false,
    write_assistant: false,
    popup: true,
    options: true,
  },
}

// Check if a feature is enabled for current product
export function isFeatureEnabled(featurePath: string): boolean {
  const product = getCurrentProduct()
  const config = productFeatures[product as keyof typeof productFeatures]

  if (!config) {
    console.warn(
      `Unknown product: ${product}, defaulting to funblocks features`
    )
    return true
  }

  const pathParts = featurePath.split('.')
  let current: any = config

  for (const part of pathParts) {
    if (current && typeof current === 'object' && part in current) {
      current = current[part]
    } else {
      return false
    }
  }

  return Boolean(current)
}

// Specific feature check functions for convenience
export const ProductFeatures = {
  // Sidebar menu features
  isSidebarFeatureEnabled: (feature: string) =>
    isFeatureEnabled(`sidebarMenu.${feature}`),

  isContextualMenuEnabled: () => isFeatureEnabled('contextualMenu'),
  isWriteAssistantEnabled: () => isFeatureEnabled('write_assistant'),

  // Widget features
  isWidgetEnabled: (widget: string) => isFeatureEnabled(`widgets.${widget}`),
  isYoutubeSummaryButtonEnabled: () =>
    isFeatureEnabled('widgets.youtube.showSummaryButton'),

  // General features
  isPopupEnabled: () => isFeatureEnabled('popup'),
  isOptionsEnabled: () => isFeatureEnabled('options'),

  // Product identification
  isFunBlocks: () => getCurrentProduct() === 'funblocks',
  isMindMap: () => getCurrentProduct() === 'mindmap',

  // ID generation utilities
  getProductId: (baseId: string) => getProductId(baseId),
  getProductClass: (baseClass: string) => getProductClass(baseClass),
}
