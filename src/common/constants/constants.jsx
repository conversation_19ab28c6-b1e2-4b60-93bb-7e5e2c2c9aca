export const RIL_FOLDER = {
    ril: 0,
    read: 1,
    trashbin: 2
}

export const getDefaultRuleOp = (property) => {
    const ruleOps = FILTER_RULE_OP_TYPES.find(item => item.toProperties.includes(property));
    return ruleOps ? ruleOps.ruleOps[0].value : null;
}

export const getConstantKey = (constantObj, value) => {

    return Object.keys(constantObj)[Object.values(constantObj).indexOf(value)];
}

// let uniqueId = getUniqueId();
// export let deviceId = isWeb ? 'web' : uniqueId;
// getBaseOs().then(baseOs => deviceId = !isWeb ? uniqueId: baseOs);


export const isDEV = process.env.NODE_ENV === 'development';

export const FEEDBACK_THREAD_ID = isDEV ? '5ef2e67cb29f364747ea5411' : '';

export const isCNDomain = () => {
    return !window.location.hostname.includes('net');
}

export const getMainDomain = () => {
    let fullDomain = window.location.hostname;
    let domainParts = fullDomain.split('.'); // 拆分域名
    let mainDomain = domainParts[domainParts.length - 2] + '.' + domainParts[domainParts.length - 1];

    return mainDomain;
}


export const APP_TYPE = {
    mindmap: 'Mindmap',
}
