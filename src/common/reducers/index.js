'use strict';

import { combineReducers } from 'redux';
import loginIn from './loginReducer';
import {
  org_lists,
  group_lists,
  to_confirm_org_lists,
  slide_lists,
  deleted_slide_lists,
  doc_lists,
  dbdata_lists,
  dbview_lists,
  dbview_dataorder_lists,
  workspace_doc_lists,
  shared_doc_lists,
  private_doc_lists,
  deleted_doc_lists,
  sub_doc_lists,
  doc_history_lists,
  ril_lists,
  read_ril_lists,
  deleted_ril_lists,
  searched_ril_lists,
  searched_todo_lists,
  comment_lists,
  article_highlight_lists,
  feedback_lists,
  tag_lists,
  prompt_lists,
  public_prompt_lists,
  workspace_prompt_lists,
  validate_prompt_lists,
  pinned_prompt_lists
} from './listReducer';
import { operationStatus } from './operationStatusReducer';
import * as ActionTypes from '../constants/actionTypes';
import * as promptReducers from './promptReducer';

import { uiReducer } from './uiReducer';

const appReducer = combineReducers({
  uiState: uiReducer,
  operationStatus,
  loginIn,
  org_lists,
  group_lists,
  feedback_lists,
  prompt_lists,
  public_prompt_lists,
  workspace_prompt_lists,
  pinned_prompt_lists,
  validate_prompt_lists,
  prompts: combineReducers({ ...promptReducers }),
});

const rootReducer = (state, action) => {
  if (action.type === ActionTypes.SETTINGS.RESET_CACHE) {
    state = { loginIn: state.loginIn };
  }

  return appReducer(state, action)
}

export default rootReducer;
