export const truncateString = (str, maxChars) => {
  if (!str) return ''

  if (str.length <= maxChars) {
    return str
  } else {
    return str.substring(0, maxChars) + '...'
  }
}

export const formatDate = (dateString) => {
  const date = new Date(dateString)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}
