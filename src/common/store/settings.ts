import { useCallback, useEffect, useState } from 'react'
import { create<PERSON>ontainer } from 'unstated-next'
import browser from 'webextension-polyfill'
import { omit, uniqueId } from 'lodash-es'
import { ServiceProvider, Settings } from '../../options/types'

const key = 'funblocks-settings'

let lng = navigator.language || navigator.userLanguage

if (lng && lng.indexOf('zh-') > -1) {
  lng = 'cn'
}

if (lng != 'cn') {
  lng = 'en'
}

export const defaultSetting: Settings = {
  trigger: 'none',
  lang: lng,
  ai_respond_lng: lng,
  balloon_auto_hide: 'false',
  balloon_copy_enabled: 'true', //contextual toolbar
  widget_disabled_range: {},
  contextual_toolbar_always: 'auto',
  contextual_toolbar_hotkey: 'Shift',
  sidebar: {
    bottom: 180,
    align: 'right',
  },
}

const _useSettings = () => {
  const [settings, _setSettings] = useState<Settings>()
  const [loading, setLoading] = useState<boolean>(true)

  useEffect(() => {
    refresh()
  }, [])

  const setSettings = useCallback(
    async (newSettings: Partial<Settings>) => {
      _setSettings({
        ...settings,
        ...newSettings,
      })

      saveSetting(newSettings)
    },
    [settings]
  )

  const refresh = useCallback(async () => {
    const initSettings = async () => {
      _setSettings(await getSetting())
      setLoading(false)
    }

    initSettings()
  }, [])

  return {
    settings,
    setSettings,
    refresh,
    loading,
  }
}

const { useContainer: useSettings, Provider: SettingsProvider } =
  createContainer(_useSettings)

export { useSettings, SettingsProvider }

export const getSetting = async () => {
  const res = {
    ...((await browser.storage.local.get(key))?.[key] || {}),
    ...((await browser.storage.sync.get(key))?.[key] || {}),
  }

  patchDefaultSetting(res)
  patchCustomInstructions(res)

  if (!res.serviceProvider) {
    res.serviceProvider = ServiceProvider.FunBlocks
  }

  return res as Settings
}

export const saveSetting = async (newSettings: Partial<Settings>) => {
  const settings = {
    ...(await getSetting()),
    ...newSettings,
  }

  // 只有 customInstruction 存在本地
  const localNewSettings = settings.customInstructions
    ? {
        customInstructions: settings.customInstructions,
      }
    : null
  const remoteSettings = omit(settings, 'customInstructions')

  browser.storage.sync.set({
    [key]: remoteSettings,
  })

  if (localNewSettings) {
    browser.storage.local.set({ [key]: localNewSettings })
  }
}

const patchCustomInstructions = (setting: Settings) => {
  setting.customInstructions =
    setting.customInstructions?.map((instruction) => {
      if (typeof instruction === 'string') {
        return {
          id: uniqueId(),
          name: instruction,
          instruction: instruction,
          icon: '😄',
        }
      }

      return instruction
    }) || []
}

const patchDefaultSetting = (setting: Settings) => {
  Object.keys(defaultSetting).forEach((s) => {
    if (!setting[s]) {
      setting[s] = defaultSetting[s]
    }
  })
}
