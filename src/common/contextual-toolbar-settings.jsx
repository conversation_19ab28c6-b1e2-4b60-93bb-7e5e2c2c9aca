import { Radio, Space, Tooltip } from "antd";
import { useSettings } from "@/common/store/settings";
import { useIntl } from 'react-intl';
import { Selector } from "./selector";

const platform = navigator.userAgent.toLowerCase();
const shortcuts = platform.includes('mac') ?
    [{ label: 'Option', value: 'Alt' }, { label: 'Control', value: 'Control' }, { label: 'Shift', value: 'Shift' }] :
    [{ label: 'Alt', value: 'Alt' }, { label: 'Ctrl', value: 'Control' }, { label: 'Shift', value: 'Shift' }]

export const ContextualToolbarSettings = () => {
    const intl = useIntl()
    const { settings = {}, setSettings } = useSettings();

    return <>
        <div style={{...styles.item, paddingBottom: 4}}>
            <span style={styles.title}>
                {intl.formatMessage({ id: 'settings_quick_action' })}
            </span>
            {/* <Switch size="small" style={{ backgroundColor: is_widget_switched(action_bar_widget_id, settings) ? 'dodgerblue' : undefined }} checked={is_widget_switched(action_bar_widget_id, settings)} onChange={(checked) => switch_widget(action_bar_widget_id, checked, settings, setSettings)} /> */}
        </div>
        {/* <div style={{ ...styles.item, paddingTop: 0 }}> */}
        <Radio.Group onChange={(e) => {
            setSettings({
                ...settings,
                contextual_toolbar_always: e.target.value
            })
        }} value={settings.contextual_toolbar_always}>
            <Space direction="vertical" style={{ rowGap: 0 }}>
                <Radio value={'auto'}>
                    <div style={{ display: 'flex', flexDirection: 'column' }}>
                        <span style={{ fontSize: 15, fontWeight: 400 }}>
                            {intl.formatMessage({ id: 'balloon_auto_display' })}
                        </span>

                    </div>
                </Radio>
                <span style={{ paddingLeft: 24, fontSize: 14, color: '#666' }}>
                    {intl.formatMessage({ id: 'balloon_auto_display_desc' })}
                </span>
                <Radio value={'shortcuts'} style={{ marginTop: 8 }}>
                    <div style={{ display: 'flex', flexDirection: 'column' }}>
                        <span style={{ fontSize: 15, fontWeight: 400 }}>
                            {intl.formatMessage({ id: 'balloon_shortcut_display' })}
                        </span>

                    </div>
                </Radio>
                <div style={{ display: 'flex', flexDirection: 'row', columnGap: 5, alignItems: 'center', flexWrap: 'wrap' }}>
                    <span style={{ paddingLeft: 24, fontSize: 14, color: '#333', whiteSpace: 'nowrap' }}>
                        {intl.formatMessage({ id: 'balloon_shortcut_select_text' })}
                    </span>

                    <Selector
                        inputStyle={{
                            ...styles.selector,
                            paddingLeft: 16,
                            width: undefined

                        }}
                        onChange={(value) => {
                            setSettings({
                                ...settings,
                                contextual_toolbar_hotkey: value,
                            })
                        }}
                        value={settings.contextual_toolbar_hotkey}
                        options={shortcuts.map((t) => ({
                            label: intl.formatMessage({ id: 'balloon_shortcut_plus_hotkey' }, { hotkey: t.label }),
                            value: t.value,
                        }))}
                        disabled={settings.contextual_toolbar_always !== 'shortcuts'}
                    />
                </div>
            </Space>
        </Radio.Group>
    </>
}

const styles = {
    item: {
        flexDirection: 'row',
        alignItems: 'center',
        display: 'flex',
        paddingTop: '16px',
        columnGap: '10px',
    },
    title: {
        fontWeight: 500,
        fontSize: 15,
    },
    input: {
        padding: '3px 12px',
        border: '1px solid #ccc',
        borderRadius: 15,
        outline: 'none',
        minWidth: '400px',
        fontSize: 14,
    },
    selector: {
        border: '1px solid #ccc',
        paddingLeft: 12,
        paddingRight: 6,
        borderRadius: 15,
        width: 60,
    },
}