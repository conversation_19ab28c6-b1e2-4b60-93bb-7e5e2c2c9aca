import { getContentDom, isGmail, isTechCrunch } from "@/content/utils/content-dom"
import { Readability, isProbablyReaderable } from "@mozilla/readability"


function findShadowRoot(node) {
  if (node.shadowRoot) {
    // 如果当前节点有 shadowRoot，直接返回
    return node.shadowRoot;
  } else if (node instanceof Element) {
    // 如果当前节点是 Element 类型，继续在其子孙节点中查找
    for (var i = 0; i < node.children.length; i++) {
      var childShadowRoot = findShadowRoot(node.children[i]);
      if (childShadowRoot) {
        return childShadowRoot;
      }
    }
  }

  // 如果没有找到，返回null
  return null;
}

export const extractWebContent = async (dom = (getContentDom() || document.activeElement)) => {
  const parser = new DOMParser()
  if (window.location.hostname.includes('msn.') && dom.querySelector('article')) {
    // console.log('article.............', dom.querySelector('article'))

    let shadowRoot = findShadowRoot(dom.querySelector('article'));
    if (shadowRoot) {
      dom = shadowRoot;
    }
  }

  if (isGmail) {
    if (dom.querySelector('[class*="msg-"]')) {
      dom = dom.querySelector('[class*="msg-"]');
    }
  }

  let doc = parser.parseFromString(dom.innerHTML, 'text/html')

  if (isGmail) {
    var signatureDivs = doc.querySelectorAll("div[id*=Signature]");
    signatureDivs?.forEach(function (div) {
      div.parentNode.removeChild(div);
    });
  }

  const article =
    // isProbablyReaderable(doc, { minContentLength: 300 }) ?
    new Readability(doc).parse()

  // 判断 article 内容长度，如果长度小于 100字符，则使用 Jina AI 服务获取内容
  if (article?.textContent?.length < 100) {
    try {
      const currentUrl = window.location.href;
      const jinaResponse = await fetch(`https://r.jina.ai/${currentUrl}`);

      if (jinaResponse.ok) {
        const jinaContent = await jinaResponse.text();

        // 解析 Jina AI 返回的内容
        const lines = jinaContent.split('\n');
        let title = '';
        let content = '';
        let isMarkdownContent = false;

        for (const line of lines) {
          if (line.startsWith('Title: ')) {
            title = line.substring(7).trim();
          } else if (line.startsWith('Markdown Content:')) {
            isMarkdownContent = true;
          } else if (isMarkdownContent && line.trim()) {
            content += line + '\n';
          }
        }

        // 如果从 Jina AI 获取到了更多内容，则使用它
        if (content.length > article.textContent.length) {
          return {
            title: title || (!isTechCrunch && article?.title) || document?.title,
            textContent: content.trim(),
            content: content.trim(),
            excerpt: content.substring(0, 200).trim() + (content.length > 200 ? '...' : ''),
            byline: article?.byline || '',
            dir: article?.dir || '',
            lang: article?.lang || '',
            length: content.length,
            siteName: article?.siteName || ''
          };
        }
      }
    } catch (error) {
      console.warn('Failed to fetch content from Jina AI:', error);
      // 如果 Jina AI 请求失败，继续使用原始的 article 内容
    }
  }

  return {
    ...(article || {}),
    title: !isTechCrunch && article?.title || document?.title
  }
}