/**
 * 获取 Shadow DOM 容器的工具函数
 * 用于 antd 组件的 getPopupContainer 属性
 */
export const getShadowDOMContainer = (
  triggerNode: HTMLElement
): HTMLElement => {
  // 尝试找到 shadow root 容器
  let container: any = triggerNode
  while (container && container.parentNode) {
    if (container.parentNode.nodeType === Node.DOCUMENT_FRAGMENT_NODE) {
      // 找到了 shadow root，返回其中的容器元素
      const shadowContainer =
        container.parentNode.querySelector('[id*="container-id"]') ||
        container.parentNode.firstElementChild ||
        container.parentNode

      // 确保容器有足够高的 z-index
      const containerElement = shadowContainer as HTMLElement
      if (containerElement && containerElement.style) {
        // 设置一个很高的 z-index 来确保 tooltip 和 popover 不被遮盖
        containerElement.style.zIndex = '2147483647'
        containerElement.style.position = 'relative'
      }

      return containerElement
    }
    container = container.parentNode
  }
  // 如果没有找到 shadow root，返回默认容器
  return document.body
}

/**
 * 获取 Shadow DOM 容器的工具函数
 * 用于 antd Modal 组件的 getContainer 属性
 */
export const getShadowDOMModalContainer = (): HTMLElement => {
  // 查找 Shadow DOM 容器
  const shadowRootElement = document.querySelector('funblocks-container') ||
                           document.querySelector('mindmap-container');

  if (shadowRootElement?.shadowRoot) {
    const container = shadowRootElement.shadowRoot.querySelector('[id*="container-id"]') ||
                     shadowRootElement.shadowRoot.firstElementChild;
    if (container) {
      return container as HTMLElement;
    }
  }

  // 如果没有找到 Shadow DOM，返回默认容器
  return document.body;
}
