/**
 * 获取 Shadow DOM 容器的工具函数
 * 用于 antd 组件的 getPopupContainer 属性
 */
export const getShadowDOMContainer = (triggerNode: HTMLElement): HTMLElement => {
  // 尝试找到 shadow root 容器
  let container: any = triggerNode;
  while (container && container.parentNode) {
    if (container.parentNode.nodeType === Node.DOCUMENT_FRAGMENT_NODE) {
      // 找到了 shadow root，返回其中的容器元素
      const shadowContainer = container.parentNode.querySelector('[id*="container-id"]') || 
                             container.parentNode.firstElementChild || 
                             container.parentNode;
      return shadowContainer as HTMLElement;
    }
    container = container.parentNode;
  }
  // 如果没有找到 shadow root，返回默认容器
  return document.body;
};
