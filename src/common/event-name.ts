export const launch_funblocks = 'launch-funblocks'

export enum EventName {
  launchFunBlocks = 'launch-funblocks',
  launchFunBlocksReader = 'launch-funblocks-reader',
  launchFunBlocksWriter = 'launch-funblocks-writer',
  launchFunBlocksResultPanel = 'launchFunBlocksResultPanel',
  launchMindMapGenerator = 'launch-mindmap-generator',
  onTabUpdated = 'onTabUpdated',
  onTabActived = 'onTabActived',
  openOptionsPage = 'open-options-page',
  getToken = 'get-token',
  token = 'token',
  chat = 'chat',
  chatgptResponse = 'chatgpt-response',
  getChatGPTToken = 'get-chatgpt-token',
  stopChatGPTChat = 'stop-chatgpt-chat',
  showOptions = 'show-options',
}

export enum PortName {
  chatgptWeb = 'chatgpt web',
}
