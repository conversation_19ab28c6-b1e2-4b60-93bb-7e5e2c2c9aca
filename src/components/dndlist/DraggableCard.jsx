import { useRef, useState } from 'react'
import { useDrag, useDrop } from 'react-dnd'
import { ItemTypes } from './ItemTypes.jsx'

export const DraggableCard = ({ itemData, isHorizontal, itemTargeted, enterItem, leaveItem, moveCard, onDrop, onDroppedOutside, children, style }) => {
  const ref = useRef(null)
  const [{ handlerId }, drop] = useDrop({
    accept: ItemTypes.CARD,
    collect(monitor) {
      return {
        handlerId: monitor.getHandlerId(),
      }
    },
    hover(item, monitor) {
      if (!ref.current) {
        return
      }
      const dragIndex = item.index
      const hoverIndex = itemData.index
      // Don't replace items with themselves
      if (item.id === itemData.id) {
        return
      }
      // Determine rectangle on screen
      const hoverBoundingRect = ref.current?.getBoundingClientRect()
      // Get vertical middle
      const hoverMiddle = !isHorizontal ?
        (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2 :
        (hoverBoundingRect.right - hoverBoundingRect.left) / 2;
      // Determine mouse position
      const clientOffset = monitor.getClientOffset()
      // Get pixels to the top
      const hoverClient = !isHorizontal ?
        (clientOffset.y - hoverBoundingRect.top) :
        (clientOffset.x - hoverBoundingRect.left);


      if(dragIndex < hoverIndex) {
        if(hoverClient < hoverMiddle) {
          moveCard && moveCard(dragIndex, hoverIndex - 1);
        } else {
          moveCard && moveCard(dragIndex, hoverIndex);
        }
      } else {
        if(hoverClient < hoverMiddle) {
          moveCard && moveCard(dragIndex, hoverIndex);
        } else {
          moveCard && moveCard(dragIndex, hoverIndex + 1);
        }
      }

      if (hoverClient < hoverMiddle / 2) {
        !!leaveItem && leaveItem(item, itemData, 0);
      } else if (hoverClient > (3 * hoverMiddle / 2)) {
        !!leaveItem && leaveItem(item, itemData, 1);
      } else {
        !!enterItem && enterItem(item, itemData);
      }
    },
    drop(item, monitor) {
      if (!ref.current) {
        return
      }

      const didDrop = monitor.didDrop();
      if (didDrop) {
        return;
      }

      onDrop && onDrop(item);
    },
  })
  const [{ isDragging }, drag] = useDrag({
    type: ItemTypes.CARD,
    item: () => {
      return itemData
    },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),

    end: (item, monitor) => {
      if (!ref.current) {
        return
      }
      const dropResult = monitor.getDropResult();
      if (!dropResult && onDroppedOutside) {
        onDroppedOutside();
      }
    }
  })
  const opacity = isDragging ? 0 : 1
  drag(drop(ref))

  return (
    <div ref={ref}
      style={{
        opacity,
        backgroundColor: itemTargeted ? 'lightblue' : 'transparent',
        ...style,
      }}
      data-handler-id={handlerId}>
      {children}
    </div>
  )
}
