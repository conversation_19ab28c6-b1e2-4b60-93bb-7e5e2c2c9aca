import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
    ReactFlow,
    useNodesState,
    useEdgesState,
    addEdge,
    MiniMap,
    Controls,
    MarkerType,
    Background,
    Panel,
} from '@xyflow/react';
import { Spin } from 'antd';

import '@xyflow/react/dist/style.css';
import './node.css';
import './simple-floatingedges.css';


import SimpleFloatingEdge from './SimpleFloatingEdge';
import AINode from './AINode';
import { node_color_themes } from './ColorMenu';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { useDispatch, useSelector } from 'react-redux';
import { AIFLOW_TOAST, STRUCTURED_GENERATED_CONTENT } from '@/common/constants/actionTypes';
import { Close } from '@styled-icons/material';
import { Tooltip } from 'antd';
import { useIntl } from 'react-intl';
import { upsertDoc } from '@/common/actions/ticketAction';
import { isDEV } from '@/common/constants/constants';
import { Check, Save, Share } from '@styled-icons/bootstrap';
import { clipboard } from 'webextension-polyfill';

const snapGrid = [20, 20];
const nodeTypes = { ai_node: AINode };
const edgeTypes = { float_edge: SimpleFloatingEdge };

const defaultViewport = { x: 0, y: 0, zoom: 1.2 };

const FlowEditor = ({ ai_action, mode, content }) => {

    const [nodes, setNodes, onNodesChange] = useNodesState([]);
    const [edges, setEdges, onEdgesChange] = useEdgesState([]);
    const [rfInstance, setRfInstance] = useState(null);
    const dispatch = useDispatch();
    const intl = useIntl();
    const aiflow_toast = useSelector(state => state.uiState.aiflow_toast);
    const operationStatus = useSelector(state => state.operationStatus);
    // const toast = useToast();

    const addNewNode = useCallback((data, nodes, edges, addToHead) => {
        let node = {
            id: (new Date().getTime() + nodes.length) + '',
            type: 'ai_node',
            data: {
                aigc_triggered: true,
                nodeType: 'aigc',
                ...data
            }
        };
        if (nodes.length) {
            edges.push({
                id: (new Date().getTime() + edges.length) + '',
                source: addToHead ? node.id : nodes[0].id,
                target: addToHead ? nodes[0].id : node.id,
                type: 'float_edge',
                markerEnd: { type: MarkerType.ArrowClosed }
            })
        }
        if (addToHead) {
            nodes.unshift(node);
        } else {
            nodes.push(node);
        }

        return node;
    }, [])

    useEffect(() => {
        if (!content) return;

        let nodes = [];
        let edges = [];


        const generated = content;

        if (ai_action === 'breakdown') {
            addNewNode({
                aigc_done: false,
                content: generated.main_subject,
                items: generated.subtopics,
                title: 'Breakdown',
            }, nodes, edges)
        } else if (ai_action === 'flow_task_breakdown') {
            const timeStamp = new Date().getTime();
            let priorities = []
            generated.items?.forEach(item => {
                if (!priorities.includes(item.priority)) {
                    priorities.push(item.priority)
                }
            })

            if (!priorities.length) {
                priorities = ['high', 'medium', 'low'];
            }

            addNewNode({
                aigc_done: false,
                content: '',
                contentType: 'todos',
                todos: generated.items?.map((item, index) => {
                    item.id = timeStamp + index;
                    return item;
                }),
                priorities: priorities.length > 0 ? priorities : undefined,
            }, nodes, edges)
        } else if (ai_action === 'summary_keypoints') {
            addNewNode({
                aigc_done: false,
                title: 'Summary and key points',
                content: generated.summary,
                items: generated.keypoints
            }, nodes, edges)

        } else {
            let rootNode = addNewNode({
                // aigc_done: false,
                ai_action,
                queryType: ['movie', 'book', 'link', 'video'].includes(mode) && mode || 'brainstorming',
                content: generated?.central_topic || generated?.core_story || generated?.initial_analysis || generated?.decision || generated?.description || generated?.summary,
                title: generated?.theme || generated?.title || generated?.problem || 'FunBlocks AI Mindmap',
                brainstorming_scenario: generated?.target_scenario,
                is_mindmap_root: true
            }, nodes, edges);

            const perspectives = generated?.key_perspectives || generated?.primary_branches || generated?.branches;
            if (perspectives?.length > 0) {
                // const positions = getSubnodesPositions(node, perspectives.length);

                perspectives.map((item, index) => {
                    if (!item.branches) return;

                    const color_theme = node_color_themes[index % node_color_themes.length];

                    addNewNode({
                        queryType: ['flow_subtopic_brainstorming', 'flow_brainstorming', 'flow_decision_analysis'].includes(ai_action) && 'perspective' || ['flow_mindmap', 'flow_book_mindmap', 'flow_movie_mindmap', 'describe_image_mindmap'].includes(ai_action) && 'mindmap_primary_branch' || 'perspective',
                        ai_action: ['flow_subtopic_brainstorming', 'flow_brainstorming', 'flow_decision_analysis'].includes(ai_action) && 'brainstorming_perspective' || ai_action === 'flow_mindmap' && 'mindmap_primary_branch' || ai_action === 'flow_book_mindmap' && 'book_mindmap_primary_branch' || ai_action === 'flow_movie_mindmap' && 'movie_mindmap_primary_branch' || ai_action === 'describe_image_mindmap' && 'image_mindmap_primary_branch' || 'brainstorming_perspective',
                        title: item.name,
                        items: item.branches,
                        userInput: item.name,
                        context_node_id: rootNode.id,
                        // context: { topic: generated.central_topic || node.data.title },
                        color_theme: color_theme?.id,
                    }, nodes, edges);
                })
            }

            const insights = generated?.summary_insights || generated?.key_insights || generated?.transformative_insights || generated?.insights
            if (insights) {
                addNewNode({
                    queryType: 'brainstorming_insights',
                    // ai_action: 'brainstorming_perspective',
                    context_node_id: rootNode.id,
                    title: 'Summary insights',
                    items: insights,
                    // context: { topic: generated.central_topic || node.data.title },
                    color_theme: node_color_themes.find(theme => theme.id === 'blue')?.id,
                }, nodes, edges);
            }
        }

        nodes = nodes.map((node, index) => {
            if (index === 0) {
                node.position = { x: 0, y: 0 };
            } else {
                const angle = - (index - 1) * (2 * Math.PI / (nodes.length - 1));
                const radius = 650; // You can adjust the radius as needed
                node.position = {
                    x: radius * Math.cos(angle),
                    y: radius * Math.sin(angle) - (index > nodes.length / 2 ? 300 : 0)
                };
            }
            return node;
        });

        // if (nodes && ['video', 'link', 'image', 'others'].includes(mode)) {
        //     let contextNode = addNewNode({
        //         nodeType: mode === 'video' && 'video' || mode === 'image' && 'image' || mode === 'others' && 'note' || 'aigc',
        //         queryType: ['video', 'link', 'image'].includes(mode) && mode || undefined,
        //         vid: context.vid,
        //         context: ['others', 'image'].includes(mode) ? undefined : context,
        //         title: context.title,
        //         content: mode === 'others' ? context.userInput : (mode === 'image' ? context : undefined),
        //         userInput: mode === 'others' ? context.userInput : undefined
        //     }, nodes, edges, true);

        //     nodes[0].position = {
        //         x: -900,
        //         y: 0
        //     }

        //     nodes[1].data.context_node_id = contextNode.id;
        // }

        // console.log('nodes......', nodes, edges)

        setNodes(nodes);
        setEdges(edges);
        // onDocSaved(null);

    }, [content])

    const [loading, setLoading] = useState(null);

    useEffect(() => {
        if (!operationStatus?.inProgress) {
            setLoading(null);
        }
    }, [operationStatus])

    const showToast = useCallback((toast) => {
        dispatch({
            type: AIFLOW_TOAST,
            value: toast
        })

        setTimeout(() => {
            dispatch({
                type: AIFLOW_TOAST,
                value: null
            })
        }, 3000)
    }, [])

    const [doc, setDoc] = useState(null);

    const saveToAIFlow = useCallback((savedCallback) => {
        if (doc?.hid) {
            savedCallback && savedCallback(doc);
            return;
        }

        let flow = rfInstance.toObject()
        const jsonString = JSON.stringify(flow);

        let data = {
            doc: {
                type: 'flow',
                title: nodes[0].data.userInput || nodes[0].data.title,
                jsonString,
                app: 'widget',
            },
            source: 'extension'
        };

        dispatch(
            upsertDoc(
                {
                    data,
                },
                (doc) => {
                    setDoc(doc);
                    savedCallback && savedCallback(doc);
                },
                'widget'
            )
        );
    }, [nodes, dispatch, rfInstance, doc])

    const continueExplore = useCallback(async () => {
        setLoading('explore');
        saveToAIFlow(async (doc) => {
            setLoading(null);
            window.open(`${isDEV ? 'http://localhost:3000' : 'https://app.funblocks.net'}/#/aiflow?hid=${doc.hid}`)
        }, true)
    }, [saveToAIFlow])

    const generateShareLink = useCallback(async () => {
        setLoading('share');
        saveToAIFlow(async (doc) => {
            setLoading(null);
            await navigator.clipboard.writeText(`${isDEV ? 'http://localhost:3000' : 'https://app.funblocks.net'}/#/aiflow?hid=${doc.hid}`)
            showToast({
                msg: intl.formatMessage({ id: 'url_generated_and_copied_to_clipboard' }),
                done: true
            })  
        })
    }, [saveToAIFlow, showToast, intl])

    const onConnect = useCallback(
        (params) =>
            setEdges((eds) =>
                addEdge({ ...params, animated: true }, eds),
            ),
        [],
    );


    if (!nodes?.length) {
        return null;
    }

    return (
        // <DndProvider backend={HTML5Backend}>
        <div
            style={{
                backgroundColor: '#f8f8f8',
                position: 'fixed',
                top: 0,
                left: 0,
                width: '100vw',
                height: '100vh',
                zIndex: 999999999999
            }}
        >
            <ReactFlow
                nodes={nodes}
                edges={edges}
                onNodesChange={onNodesChange}
                onEdgesChange={onEdgesChange}
                onConnect={onConnect}
                onInit={setRfInstance}
                nodeTypes={nodeTypes}
                edgeTypes={edgeTypes}
                snapToGrid={true}
                snapGrid={snapGrid}
                defaultViewport={defaultViewport}
                fitView
                attributionPosition="bottom-left"
                style={{
                    borderRadius: 6
                }}
            >
                <Background style={{ backgroundColor: '#f8f8f8' }} />
                <Controls />

                <Panel
                    position='top-right'
                    style={{
                        display: 'flex',
                        flexDirection: 'row',
                        alignItems: 'center',
                        marginTop: 0,
                        marginRight: 0,
                        position: 'absolute',
                        right: 20,
                        top: 10,
                        gap: 10,
                        zIndex: 99999999
                    }}>
                    {/* <div
                        className='board-operations'
                        style={button_group_style}
                    > */}

                    <div
                        className='hoverStand'
                        style={{
                            cursor: !!loading? 'not-allowed' : 'pointer',
                            zIndex: 999999999,
                            opacity: !!loading ? 0.7 : 1,
                            display: 'flex',
                            flexDirection: 'row',
                            alignItems: 'center',
                            gap: '8px'
                        }}
                        onClick={() => {
                            if (!!loading) return;

                            if (!doc?.hid) {
                                setLoading('saving');

                                saveToAIFlow((doc)=>{
                                    setLoading(null);
                                    showToast({
                                        msg: intl.formatMessage({ id: 'saved_to_aiflow' }, { title: doc.title }),
                                        done: true
                                    })
                                })
                            } else {
                                window.open(`${isDEV ? 'http://localhost:3000' : 'https://app.funblocks.net'}/#/aiflow?hid=${doc.hid}`)
                            }
                        }}
                    >
                        {!doc?.hid && <Save size={20} color={!!loading ? '#999' : 'dodgerblue'} />}
                        {loading === 'saving' && <Spin size="small" />}
                        {doc?.hid && <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', gap: 4 }}>
                            <div
                                style={{
                                    color: '#666',
                                    maxWidth: 360,
                                }}
                            >
                                Saved to AIFlow:&nbsp;
                            </div>
                            <div
                                style={{
                                    color: 'dodgerblue',
                                    maxWidth: 360,
                                    overflow: 'hidden',
                                    textOverflow: 'ellipsis',
                                    whiteSpace: 'nowrap',
                                    textDecoration: 'underline'
                                }}
                            >
                                {doc.title}
                            </div>
                        </div>}
                    </div>

                    <div
                        className='hoverStand'
                        style={{
                            cursor: !!loading ? 'not-allowed' : 'pointer',
                            zIndex: 999999999,
                            opacity: !!loading ? 0.7 : 1,
                            display: 'flex',
                            flexDirection: 'row',
                            alignItems: 'center',
                            gap: '8px'
                        }}
                        onClick={() => {
                            if (!!loading) return;

                            generateShareLink()
                        }}
                    >
                        <Share size={20} color={!!loading ? '#999' : 'dodgerblue'} />
                        {loading === 'share' && <Spin size="small" />}
                    </div>

                    <div
                        className='hoverStand'
                        style={{
                            color: !!loading ? '#999' : 'dodgerblue',
                            border: `1px solid ${!!loading ? '#999' : 'dodgerblue'}`,
                            padding: '6px 12px',
                            borderRadius: 26,
                            cursor: !!loading ? 'not-allowed' : 'pointer',
                            zIndex: 999999999,
                            opacity: !!loading ? 0.7 : 1,
                            display: 'flex',
                            flexDirection: 'row',
                            alignItems: 'center',
                            gap: '8px'
                        }}
                        onClick={() => {
                            if (!!loading) return;

                            continueExplore()
                        }}
                    >
                        {intl.formatMessage({ id: 'continue_explore_with_funblocks' })}
                        {loading === 'explore' && <Spin size="small" />}
                    </div>
                    {/* </div> */}

                    <div
                        className='hoverStand'
                        style={{
                            cursor: 'pointer',
                            zIndex: 999999999
                        }}
                        onClick={() => {
                            dispatch({
                                type: STRUCTURED_GENERATED_CONTENT,
                                value: null
                            })
                        }}
                    >
                        <Close size={20} color='black' />
                    </div>
                </Panel>


                {
                    aiflow_toast &&
                    <div
                        style={{
                            display: 'flex',
                            flexDirection: 'column',
                            alignItems: 'center',
                            justifyContent: 'center',
                            borderRadius: '6px',
                            maxWidth: '300px',
                            padding: '12px 16px',
                            boxShadow: '0px 0px 8px #bbb',
                            transition: 'box-shadow 0.3s ease-in-out',
                            backgroundColor: '#fafafa',
                            color: '#333',
                            position: 'absolute',
                            bottom: 20,
                            left: '50%',
                            transform: 'translateX(-50%)',
                            zIndex: 999999999999,
                            pointerEvents: 'auto'
                        }}
                    >
                        <div
                            style={{
                                display: 'flex',
                                flexDirection: 'row',
                                width: '100%',
                                alignItems: 'flex-start',
                                justifyContent: 'space-between',
                                marginBottom: aiflow_toast.action === 'to_aiflow' ? '8px' : 0,
                            }}
                        >
                            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                                <div>{aiflow_toast.msg}</div>
                                {aiflow_toast.done && <Check size={28} color="green" />}
                            </div>
                            {
                                !aiflow_toast.done &&
                                <Close
                                    size={20}
                                    color='#666'
                                    style={{ cursor: 'pointer' }}
                                    onClick={() => {
                                        dispatch({
                                            type: AIFLOW_TOAST,
                                            value: null
                                        })
                                    }}
                                />
                            }

                        </div>
                        {
                            aiflow_toast.action === 'to_aiflow' && (
                                <button
                                    style={{
                                        backgroundColor: 'dodgerblue',
                                        color: 'white',
                                        border: 'none',
                                        padding: '6px 12px',
                                        borderRadius: '4px',
                                        cursor: loading === 'saving' ? 'not-allowed' : 'pointer',
                                        fontSize: '14px',
                                        width: '100%',
                                        marginTop: '4px',
                                        opacity: loading === 'saving' ? 0.7 : 1,
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        gap: '8px'
                                    }}
                                    onClick={loading === 'saving' ? undefined : continueExplore}
                                >
                                    {intl.formatMessage({ id: 'continue_explore_with_funblocks' })}
                                    {loading === 'saving' && <Spin size="small" />}
                                </button>
                            )}
                    </div>
                }
                {/* <Tooltip
                    title={intl.formatMessage({ id: 'close' })}
                    placement='bottom'
                > */}

                {/* </Tooltip> */}

            </ReactFlow>
        </div>
        // </DndProvider>
    );
};

export default FlowEditor;