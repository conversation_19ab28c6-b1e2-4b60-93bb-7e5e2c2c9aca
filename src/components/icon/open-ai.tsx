import { SVGProps } from 'react'

export const OpenAILogo: React.FC<SVGProps<SVGSVGElement>> = (props) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="1280"
      height="314.982"
      viewBox="0 0 338.667 83.339"
      fill="currentColor"
      {...props}
    >
      <path d="M200.154 32.427v.028c-.169 0-.339.028-.508.028s-.339-.028-.508-.028c-10.075 0-16.312 6.294-16.312 16.397v4.967c0 9.736 6.322 15.776 16.453 15.776a4.38 4.38 0 0 0 .621-.028c.141 0 .254.028.395.028 6.801 0 11.543-2.483 14.562-7.62l-6.011-3.472c-2.004 2.963-4.685 5.193-8.523 5.193-5.136 0-8.212-3.161-8.212-8.495V53.79h23.819v-5.87c0-9.426-6.18-15.494-15.776-15.494zm-.508 5.786c4.685.226 7.507 3.33 7.507 8.438v1.411h-15.07v-.819c0-5.644 2.681-8.805 7.563-9.031zm-36.998-5.758c-4.487 0-8.353 1.863-10.385 4.967l-.508.79v-4.911h-8.523v47.667h8.946v-16.65l.508.762c1.919 2.85 5.673 4.543 10.047 4.543h.226.197c7.366 0 14.788-4.798 14.788-15.55v-6.039c0-7.733-4.572-15.55-14.845-15.55l-.056-.028h-.197zm-2.088 6.717c5.193.085 8.41 3.612 8.41 9.257v5.192c0 5.644-3.246 9.144-8.495 9.257-4.882-.085-8.297-3.81-8.297-9.116v-5.334c0-5.362 3.443-9.144 8.382-9.257zm115.934-18.485l-17.215 48.09h9.68l3.302-10.301h18.795v.113l3.302 10.216h9.68l-17.243-48.09h-1.016l-.028-.028zm5.137 8.269l7.196 22.719h-14.45zm57.035-1.496v-6.773h-29.52v6.773h10.357v34.487h-10.357v6.773h29.52v-6.773h-10.357V27.46zm-97.139 4.996h-.254-.141c-4.995 0-8.551 1.693-10.301 4.939l-.536.988v-5.08h-8.523v35.446h8.946v-21.11c0-4.967 2.681-7.817 7.309-7.902 4.431.085 6.971 2.879 6.971 7.705v21.307h8.946V45.917c0-8.438-4.628-13.462-12.389-13.462zM114.473 19.699c-13.18 0-21.392 8.213-21.392 21.449v7.14c0 13.236 8.184 21.448 21.392 21.448h.198.197c13.18 0 21.392-8.212 21.392-21.448v-7.14c0-13.236-8.212-21.449-21.392-21.449h-.197zm.198 7.169c7.846.085 12.361 5.108 12.361 13.8v8.128c0 8.692-4.515 13.716-12.361 13.8-7.846-.085-12.362-5.108-12.362-13.8v-8.128c0-8.692 4.516-13.716 12.362-13.8zM36.751.001c-9.116 0-17.215 5.87-20.038 14.534A20.83 20.83 0 0 0 2.828 24.61C-1.744 32.512-.7 42.446 5.425 49.219 3.534 54.892 4.183 61.1 7.203 66.237c4.544 7.93 13.687 11.994 22.634 10.103a20.78 20.78 0 0 0 15.635 6.999c9.116 0 17.215-5.87 20.038-14.534 5.87-1.214 10.922-4.883 13.857-10.075 4.6-7.902 3.556-17.836-2.568-24.609v-.028a20.76 20.76 0 0 0-1.778-17.046C70.476 9.145 61.332 5.08 52.414 6.971A20.86 20.86 0 0 0 36.751.001zm0 5.419l-.028.028c3.669 0 7.197 1.27 10.019 3.613-.113.056-.339.197-.508.282L29.64 18.91c-.847.48-1.355 1.383-1.355 2.371v22.464l-7.14-4.12v-18.57A15.63 15.63 0 0 1 36.751 5.419zm19.99 6.54a15.62 15.62 0 0 1 13.566 7.825c1.806 3.161 2.483 6.858 1.862 10.442-.113-.085-.338-.197-.48-.282l-16.594-9.596a2.78 2.78 0 0 0-2.737 0L32.913 31.581V23.34l16.058-9.285a15.54 15.54 0 0 1 7.77-2.096zm-41.043 8.53v19.727c0 .988.508 1.863 1.355 2.371l19.416 11.204L29.3 57.94l-16.03-9.257a15.63 15.63 0 0 1-5.7-21.336 15.65 15.65 0 0 1 8.128-6.858zm37.196 4.882l16.058 9.257c7.479 4.318 10.018 13.857 5.7 21.336l.028.028c-1.834 3.161-4.713 5.588-8.128 6.83V43.095c0-.988-.508-1.891-1.355-2.37L45.753 29.492zm-11.797 6.802l8.185 4.741v9.454l-8.185 4.741-8.184-4.741v-9.454zm12.869 7.451l7.14 4.12v18.542c0 8.636-6.999 15.635-15.606 15.635v-.028c-3.641 0-7.197-1.27-9.991-3.612.113-.056.367-.198.508-.283l16.594-9.567c.847-.48 1.383-1.383 1.354-2.371zM49.309 51.76V60l-16.058 9.257c-7.479 4.29-17.018 1.75-21.336-5.701h.028c-1.834-3.133-2.484-6.858-1.863-10.442.113.085.339.197.48.282l16.594 9.596a2.78 2.78 0 0 0 2.737 0z" />
    </svg>
  )
}
