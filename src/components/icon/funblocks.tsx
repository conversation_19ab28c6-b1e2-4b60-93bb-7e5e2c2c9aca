import React, { SVGProps } from 'react'

export const IconFunBlocks: React.FC<SVGProps<SVGSVGElement>> = (props) => {
  return (
    <svg
      version="1.0"
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 192.000000 192.000000"
      preserveAspectRatio="xMidYMid meet"
      {...props}
    >
      <g
        transform="translate(0.000000,192.000000) scale(0.100000,-0.100000)"
        fill="currentColor"
        stroke="none"
      >
        <path
          d="M353 1906 c-172 -42 -306 -181 -342 -354 -7 -35 -11 -238 -11 -595 0
 -591 3 -618 58 -721 58 -108 183 -199 310 -225 35 -7 238 -11 595 -11 591 0
 618 3 721 58 64 35 143 114 178 178 55 103 58 130 58 721 0 357 -4 560 -11
 595 -26 127 -117 252 -225 310 -103 56 -129 58 -729 57 -404 -1 -564 -4 -602
 -13z m1262 -110 c76 -37 142 -104 180 -181 l30 -60 0 -595 0 -595 -27 -57
 c-37 -79 -98 -142 -177 -181 l-66 -32 -595 0 -595 0 -60 29 c-76 38 -143 105
 -181 181 l-29 60 0 585 c0 560 1 587 20 640 23 64 115 168 173 196 88 42 103
 43 697 41 l570 -2 60 -29z"
        />
        <path
          d="M244 1583 c3 -10 15 -63 26 -118 11 -55 27 -129 35 -165 8 -36 42
 -195 75 -355 33 -159 67 -319 75 -355 7 -36 21 -96 29 -135 8 -38 18 -85 21
 -103 l7 -33 96 3 95 3 102 415 c56 228 113 465 126 525 12 61 24 112 26 114 7
 10 13 -10 33 -109 12 -58 68 -293 126 -522 57 -229 104 -420 104 -423 0 -3 44
 -5 98 -5 l97 0 58 283 c32 155 80 390 107 522 28 132 59 285 70 340 12 55 23
 108 26 118 5 15 -3 17 -73 17 l-78 0 -28 -147 c-15 -82 -45 -240 -67 -353 -44
 -225 -74 -390 -95 -519 -8 -46 -16 -86 -19 -89 -8 -7 -10 1 -56 213 -38 174
 -66 289 -181 733 -21 84 -39 154 -39 157 0 3 -33 5 -73 5 l-73 0 -22 -87 c-12
 -49 -53 -209 -91 -358 -37 -148 -88 -359 -112 -467 -24 -109 -46 -198 -49
 -198 -3 0 -13 46 -23 103 -32 177 -124 666 -166 872 l-26 130 -83 3 c-76 3
 -83 1 -78 -15z"
        />
      </g>
    </svg>
  )
}
