import { SVGProps } from 'react';

export function IcBaselineAutoFixHigh(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
      viewBox="0 0 24 24"
      {...props}
    >
      <path
        fill="currentColor"
        d="M7.5 5.6L10 7L8.6 4.5L10 2L7.5 3.4L5 2l1.4 2.5L5 7zm12 9.8L17 14l1.4 2.5L17 19l2.5-1.4L22 19l-1.4-2.5L22 14zM22 2l-2.5 1.4L17 2l1.4 2.5L17 7l2.5-1.4L22 7l-1.4-2.5zm-7.63 5.29a.996.996 0 0 0-1.41 0L1.29 18.96a.996.996 0 0 0 0 1.41l2.34 2.34c.39.39 1.02.39 1.41 0L16.7 11.05a.996.996 0 0 0 0-1.41l-2.33-2.35zm-1.03 5.49l-2.12-2.12l2.44-2.44l2.12 2.12l-2.44 2.44z"
      ></path>
    </svg>
  );
}

export function IcBaselineCheck(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
      viewBox="0 0 24 24"
      {...props}
    >
      <path
        fill="currentColor"
        d="M9 16.17L4.83 12l-1.42 1.41L9 19L21 7l-1.41-1.41z"
      ></path>
    </svg>
  );
}

export function IcBaselineShortText(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
      viewBox="0 0 24 24"
      {...props}
    >
      <path fill="currentColor" d="M4 9h16v2H4V9zm0 4h10v2H4v-2z"></path>
    </svg>
  );
}

export function MdiTextLong(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
      viewBox="0 0 24 24"
      {...props}
    >
      <path
        fill="currentColor"
        d="M4 5h16v2H4V5m0 4h16v2H4V9m0 4h16v2H4v-2m0 4h10v2H4v-2Z"
      ></path>
    </svg>
  );
}

export function MdiTreeOutline(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
      viewBox="0 0 24 24"
      {...props}
    >
      <path
        fill="currentColor"
        d="M10.5 3a4.481 4.481 0 0 0-4.13 6.27C5.5 10.12 5 11.28 5 12.5C5 15 7 17 9.5 17c.5 0 1-.11 1.5-.28V21h2v-5.23c.5.14 1 .23 1.5.23a5.5 5.5 0 0 0 5.5-5.5A5.5 5.5 0 0 0 14.5 5h-.26C13.41 3.76 12 3 10.5 3m0 2c1.32 0 2.41 1.03 2.5 2.35c.46-.23 1-.35 1.5-.35a3.5 3.5 0 0 1 3.5 3.5a3.5 3.5 0 0 1-3.5 3.5c-.96 0-1.87-.39-2.54-1.09A2.491 2.491 0 0 1 9.5 15A2.5 2.5 0 0 1 7 12.5c0-1.38.8-1.96 2-2.71c-.8-1.03-1-1.63-1-2.29A2.5 2.5 0 0 1 10.5 5Z"
      ></path>
    </svg>
  );
}

export function IcOutlineAutoStories(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
      viewBox="0 0 24 24"
      {...props}
    >
      <path
        fill="currentColor"
        d="M22.47 5.2c-.47-.24-.96-.44-1.47-.61v12.03c-1.14-.41-2.31-.62-3.5-.62c-1.9 0-3.78.54-5.5 1.58V5.48C10.38 4.55 8.51 4 6.5 4c-1.79 0-3.48.44-4.97 1.2c-.33.16-.53.51-.53.88v12.08c0 .58.47.99 1 .99c.16 0 .32-.04.48-.12C3.69 18.4 5.05 18 6.5 18c2.07 0 3.98.82 5.5 2c1.52-1.18 3.43-2 5.5-2c1.45 0 2.81.4 4.02 1.04c.16.08.32.12.48.12c.52 0 1-.41 1-.99V6.08c0-.37-.2-.72-.53-.88zM10 16.62C8.86 16.21 7.69 16 6.5 16s-2.36.21-3.5.62V6.71C4.11 6.24 5.28 6 6.5 6c1.2 0 2.39.25 3.5.72v9.9zM19 .5l-5 5V15l5-4.5V.5z"
      ></path>
    </svg>
  );
}

export function IcOutlineTranslate(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
      viewBox="0 0 24 24"
      {...props}
    >
      <path
        fill="currentColor"
        d="m12.87 15.07l-2.54-2.51l.03-.03A17.52 17.52 0 0 0 14.07 6H17V4h-7V2H8v2H1v1.99h11.17C11.5 7.92 10.44 9.75 9 11.35C8.07 10.32 7.3 9.19 6.69 8h-2c.73 1.63 1.73 3.17 2.98 4.56l-5.09 5.02L4 19l5-5l3.11 3.11l.76-2.04zM18.5 10h-2L12 22h2l1.12-3h4.75L21 22h2l-4.5-12zm-2.62 7l1.62-4.33L19.12 17h-3.24z"
      ></path>
    </svg>
  );
}

export function IcSharpPanoramaWideAngle(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
      viewBox="0 0 24 24"
      {...props}
    >
      <path
        fill="currentColor"
        d="M12 6c2.45 0 4.71.2 7.29.64c.47 1.78.71 3.58.71 5.36s-.24 3.58-.71 5.36c-2.58.44-4.84.64-7.29.64s-4.71-.2-7.29-.64C4.24 15.58 4 13.78 4 12s.24-3.58.71-5.36C7.29 6.2 9.55 6 12 6m0-2c-2.73 0-5.22.24-7.95.72l-.93.16l-.25.9C2.29 7.85 2 9.93 2 12s.29 4.15.87 6.22l.25.89l.93.16c2.73.49 5.22.73 7.95.73s5.22-.24 7.95-.72l.93-.16l.25-.89c.58-2.08.87-4.16.87-6.23s-.29-4.15-.87-6.22l-.25-.89l-.93-.16C17.22 4.24 14.73 4 12 4z"
      ></path>
    </svg>
  );
}

export function IcOutlineLightbulb(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
      viewBox="0 0 24 24"
      {...props}
    >
      <path
        fill="currentColor"
        d="M9 21c0 .55.45 1 1 1h4c.55 0 1-.45 1-1v-1H9v1zm3-19C8.14 2 5 5.14 5 9c0 2.38 1.19 4.47 3 5.74V17c0 .55.45 1 1 1h6c.55 0 1-.45 1-1v-2.26c1.81-1.27 3-3.36 3-5.74c0-3.86-3.14-7-7-7zm2.85 11.1l-.85.6V16h-4v-2.3l-.85-.6A4.997 4.997 0 0 1 7 9c0-2.76 2.24-5 5-5s5 2.24 5 5c0 1.63-.8 3.16-2.15 4.1z"
      ></path>
    </svg>
  );
}

export function MdiMessageReplyTextOutline(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
      viewBox="0 0 24 24"
      {...props}
    >
      <path
        fill="currentColor"
        d="M9 11h9v2H9v-2m9-4H6v2h12V7m4-3v18l-4-4H4a2 2 0 0 1-2-2V4c0-1.1.9-2 2-2h16a2 2 0 0 1 2 2m-2 0H4v12h14.83L20 17.17V4Z"
      ></path>
    </svg>
  );
}

export function IcBaselinePodcasts(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
      viewBox="0 0 24 24"
      {...props}
    >
      <path
        fill="currentColor"
        d="M14 12c0 .74-.4 1.38-1 1.72V22h-2v-8.28c-.6-.35-1-.98-1-1.72c0-1.1.9-2 2-2s2 .9 2 2zm-2-6c-3.31 0-6 2.69-6 6c0 1.74.75 3.31 1.94 4.4l1.42-1.42A3.957 3.957 0 0 1 8 12c0-2.21 1.79-4 4-4s4 1.79 4 4c0 1.19-.53 2.25-1.36 2.98l1.42 1.42A5.957 5.957 0 0 0 18 12c0-3.31-2.69-6-6-6zm0-4C6.48 2 2 6.48 2 12c0 2.85 1.2 5.41 3.11 7.24l1.42-1.42A7.987 7.987 0 0 1 4 12c0-4.41 3.59-8 8-8s8 3.59 8 8c0 2.29-.98 4.36-2.53 5.82l1.42 1.42C20.8 17.41 22 14.85 22 12c0-5.52-4.48-10-10-10z"
      ></path>
    </svg>
  );
}

export function MaterialSymbolsEnergyProgramTimeUsedSharp(
  props: SVGProps<SVGSVGElement>
) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
      viewBox="0 0 24 24"
      {...props}
    >
      <path
        fill="currentColor"
        d="M3 22q-.825 0-1.412-.587Q1 20.825 1 20V6q0-.825.588-1.412Q2.175 4 3 4h8v2H3v14h14v-6h2v6q0 .825-.587 1.413Q17.825 22 17 22Zm2-4v-7h2v7Zm4 0V8h2v10Zm4 0v-4h2v4Zm5-6q-.8 0-1.512-.225q-.713-.225-1.313-.65l-.375.35q-.3.3-.7.3q-.4 0-.7-.3q-.3-.3-.3-.7q0-.4.3-.7l.4-.4q-.375-.575-.587-1.25Q13 7.75 13 7q0-2.15 1.488-3.575Q15.975 2 18 2h5v5q0 2-1.425 3.5T18 12Zm0-2q1.25 0 2.125-.875T21 7V4h-3q-1.2 0-2.1.85Q15 5.7 15 7q0 .325.062.637q.063.313.188.588l2.6-2.6q.3-.3.7-.3q.4 0 .7.3q.3.3.3.713q0 .412-.3.712l-2.6 2.6q.3.15.638.25q.337.1.712.1Z"
      ></path>
    </svg>
  );
}

export function MaterialSymbolsToolsPliersWireStripperOutlineSharp(
  props: SVGProps<SVGSVGElement>
) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
      viewBox="0 0 24 24"
      {...props}
    >
      <path
        fill="currentColor"
        d="M7 23q-.5-1-.75-2.45Q6 19.1 6 17.6q0-2.475.55-4.088Q7.1 11.9 8 11V6l3-5h2l3 5v5q.925.9 1.462 2.512Q18 15.125 18 17.6q0 1.5-.25 2.95Q17.5 22 17 23q-1.05-.325-1.675-1.188q-.625-.862-.6-1.962q0-.625.138-1.35q.137-.725.137-1.45q0-1.45-.787-3.05q-.788-1.6-2.213-3q-1.4 1.4-2.2 3q-.8 1.6-.8 3.05q0 .725.15 1.45t.175 1.35q0 1.1-.637 1.975Q8.05 22.7 7 23Zm5-15q-.425 0-.712-.287Q11 7.425 11 7q0-.275.125-.5q.125-.225.375-.35V4.025L10 6.55V9h4V6.55l-1.5-2.525V6.15q.25.125.375.35q.125.225.125.5q0 .425-.287.713Q12.425 8 12 8Zm.5-3.975h-1h1Zm-1 0h1Z"
      ></path>
    </svg>
  );
}

export function IcRoundQuestionMark(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
      viewBox="0 0 24 24"
      {...props}
    >
      <path
        fill="currentColor"
        d="M7.92 7.54c-.8-.34-1.14-1.33-.66-2.05C8.23 4.05 9.85 3 11.99 3c2.35 0 3.96 1.07 4.78 2.41c.7 1.15 1.11 3.3.03 4.9c-1.2 1.77-2.35 2.31-2.97 3.45c-.15.27-.24.49-.3.94c-.09.73-.69 1.3-1.43 1.3c-.87 0-1.58-.75-1.48-1.62c.06-.51.18-1.04.46-1.54c.77-1.39 2.25-2.21 3.11-3.44c.91-1.29.4-3.7-2.18-3.7c-1.17 0-1.93.61-2.4 1.34c-.35.57-1.08.75-1.69.5zM14 20c0 1.1-.9 2-2 2s-2-.9-2-2s.9-2 2-2s2 .9 2 2z"
      ></path>
    </svg>
  );
}

export function MaterialSymbolsFormatListBulletedSharp(
  props: SVGProps<SVGSVGElement>
) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
      viewBox="0 0 24 24"
      {...props}
    >
      <path
        fill="currentColor"
        d="M9 19v-2h12v2Zm0-6v-2h12v2Zm0-6V5h12v2ZM5 20q-.825 0-1.413-.587Q3 18.825 3 18q0-.825.587-1.413Q4.175 16 5 16q.825 0 1.412.587Q7 17.175 7 18q0 .825-.588 1.413Q5.825 20 5 20Zm0-6q-.825 0-1.413-.588Q3 12.825 3 12t.587-1.413Q4.175 10 5 10q.825 0 1.412.587Q7 11.175 7 12q0 .825-.588 1.412Q5.825 14 5 14Zm0-6q-.825 0-1.413-.588Q3 6.825 3 6t.587-1.412Q4.175 4 5 4q.825 0 1.412.588Q7 5.175 7 6t-.588 1.412Q5.825 8 5 8Z"
      ></path>
    </svg>
  );
}
