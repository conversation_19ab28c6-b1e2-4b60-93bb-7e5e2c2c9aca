import { DashiconsAdminGeneric } from '@/components/icon'
import './index.css'
import browser from 'webextension-polyfill'
import { IntlProvider } from 'react-intl'
import { useSettings } from '../common/store/settings'
import messages from '../common/messages'
import { Menu } from './menu'

import { ProductFeatures } from '@/common/product-features'

export const App: React.FC = () => {
  const { settings } = useSettings()
  const lng = settings?.lang || 'en'

  return (
    <IntlProvider locale={lng} messages={messages[lng]}>
      <div
        className="rounded-lg overflow-hidden w-56 bg-white"
        style={{
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        <div
          className="font-semibold from-neutral-900 flex items-baseline justify-between pt-1 pb-1 border-gray-200 border-b px-3"
          style={{
            fontSize: 15,
          }}
        >
          {(ProductFeatures.isMindMap() && 'AI MindMap') ||
            'FunBlocks AI Assistant'}
        </div>
        <Menu />
      </div>
    </IntlProvider>
  )
}
