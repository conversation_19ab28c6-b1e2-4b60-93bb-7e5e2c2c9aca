import * as React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { DialogContent, Dialog, Button, DialogTitle, Fab, Divider, IconButton, Link } from '@mui/material';
import { Message } from '@styled-icons/material/Message';
import { Send } from '@styled-icons/material/Send';

import { useIntl } from 'react-intl';

import { fetchFeedbacks, feedback as submitFeedback, getFeedbackTypeOptions } from '@/common/actions/ticketAction';
import { getStateByUser } from '@/common/reducers/listReducer';
import { Selector } from '@/common/selector';
import { useSettings } from '@/common/store/settings';
import browser from 'webextension-polyfill'
import { ProductFeatures } from '@/common/product-features';

const FeedbackItem = ({ feedback }) => {
    const intl = useIntl();

    return <div style={{
        padding: 12,
        fontSize: 14,
        backgroundColor: 'white',
        width: '-webkit-fill-available'
    }}>

        {
            feedback.title
        }

        {
            !!feedback.reply &&
            <div style={{ paddingTop: 12 }}>
                <Divider />
                <div style={{ paddingTop: 12, color: '#666', fontSize: 13 }}>
                    {intl.formatMessage({ id: 'reply' }) + ": " + feedback.reply}
                </div>
            </div>
        }
    </div>
}

function generateRandomString() {
    const characters =
        'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
    let randomString = ''

    for (let i = 0; i < 16; i++) {
        const randomIndex = Math.floor(Math.random() * characters.length)
        randomString += characters.charAt(randomIndex)
    }

    return randomString
}

const FeedbackDialog = ({ dialogState, handleClose }) => {
    const dispatch = useDispatch();
    const intl = useIntl();
    const user = useSelector(state => state.loginIn.user);

    const [options, setOptions] = React.useState();
    const [data, setData] = React.useState({})
    const { settings, setSettings } = useSettings()


    React.useEffect(() => {
        dispatch(getFeedbackTypeOptions({}, (options) => {
            setOptions(options);
            setData({ type: options[0].value })
        }))
    }, []);

    React.useEffect(() => {
        if (!dialogState?.visible) return;

        if (!settings?.guestId) {
            setSettings({
                ...settings,
                guestId: generateRandomString()
            })
        }
    }, [dialogState?.visible])

    const sendFeedback = () => {
        if (!data || !data.text) {
            return;
        }

        dispatch(submitFeedback({
            data: {
                ...data,
                username: user?.nickname,
                app: 'funblocks_ext'
            },
            userId: user._id || settings.guestId
        }, () => {
            handleClose();
        }, 'feedback'));
    }

    return <Dialog
        open={!!dialogState?.visible}
        onClose={handleClose}
        scroll='paper'
        aria-labelledby="scroll-dialog-title"
        aria-describedby="scroll-dialog-description"
        maxWidth='md'
    >
        <div style={{ width: '100%', display: 'flex', flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
            <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', padding: 10, paddingBottom: 0, fontSize: 16 }}>
                {intl.formatMessage({ id: "feedback" })}
            </div>
        </div>
        <DialogContent
            style={{
                display: 'flex', flexDirection: 'column', alignItems: 'center',
                width: 600, padding: 10,
                fontSize: 14
            }}
        >
            {
                options &&
                <div
                    className='fill-available'
                    style={{
                        display: 'flex', flexDirection: 'row', justifyContent: 'flex-start', alignItems: 'center',
                        columnGap: 8, paddingBottom: 10
                    }}>
                    {intl.formatMessage({ id: 'type' })}
                    <Selector
                        onChange={(value) => {
                            setData(prevState => {
                                return {
                                    ...prevState,
                                    type: value
                                }
                            })
                        }}
                        options={options}
                        value={data.type}
                    />
                </div>
            }

            <textarea
                className='fill-available'
                style={{
                    border: '1px solid #ddd',
                    outline: 'none',
                    borderRadius: 5,
                    padding: 4
                }}
                rows={8}
                onChange={(e) => {
                    setData(prevState => {
                        return {
                            ...prevState,
                            text: e.target.value
                        }
                    })
                }} />
        </DialogContent>

        <div className='fill-available' style={{ display: 'flex', flexDirection: 'row', justifyContent: 'flex-end', paddingRight: 10, paddingBottom: 10, columnGap: 10 }}>
            <Button variant='text' onClick={handleClose}>{intl.formatMessage({ id: 'cancel' })}</Button>
            <Button size='medium' aria-label="feedback" color="primary"
                style={{ borderRadius: 5, backgroundColor: 'dodgerblue', color: 'white' }}
                onClick={sendFeedback}>
                <Send size={20} />
            </Button>
        </div>
    </Dialog>
}


const Feedbacks = () => {
    const intl = useIntl();
    const dispatch = useDispatch();
    const user = useSelector(state => state.loginIn.user);
    const feedbacks = useSelector(state => getStateByUser(state.feedback_lists, user));
    const [dialogState, setDialogState] = React.useState({});
    const { settings } = useSettings()

    const chrome_extension_store_img_path = browser.runtime.getURL(
        'assets/chrome_extension_store.png'
    )

    React.useEffect(() => {
        dispatch(fetchFeedbacks({ userId: user._id || settings.guestId }));
    }, []);

    const toggleDialog = (visible) => {
        setDialogState({ visible });
    }
    return (
        <div style={{ position: 'relative', padding: 10, paddingTop: 30 }}>
            <div className='fill-available' style={{ ...styles.section_box, display: 'flex', flexDirection: 'column', rowGap: 10, alignItems: 'center', justifyContent: 'center', marginTop: 36, marginBottom: 30 }}>

                <span style={{ fontSize: 16, marginRight: 8 }}>
                    {intl.formatMessage({ id: 'like_this_extension' })}
                </span>
                <Button
                    size='medium'
                    variant='outlined'
                    onClick={() => {
                        const extension_url = ProductFeatures.isMindMap() && '' || 'https://chromewebstore.google.com/detail/funblocks-ai-readwrite-as/coodnehmocjfaandkbeknihiagfccoid/reviews';
                        window.open(extension_url)
                    }}
                    style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', columnGap: 6, borderRadius: 20 }}
                >
                    <img
                        width={24}
                        src={chrome_extension_store_img_path}
                        draggable={false}
                    />
                    {intl.formatMessage({ id: 'go_rating' })}
                </Button>
            </div>

            {/* <div style={{ fontSize: 14, marginBottom: 8 }}>
                {intl.formatMessage({ id: 'feedback_here' })}
                <Button
                    size='medium'
                    aria-label="feedback"
                    color="primary"
                    variant='text'
                    onClick={(e) => {
                        toggleDialog(true)
                    }}
                    startIcon={<Message size={24} />}
                >
                    {intl.formatMessage({ id: 'feedback' })}
                </Button></div>
            {
                feedbacks?.items.map((feedback, i) => {
                    return <div key={i + ''}>
                        {
                            !!i &&
                            <Divider />

                        }
                        <FeedbackItem feedback={feedback} />

                    </div>
                })
            }

            <FeedbackDialog
                dialogState={dialogState}
                handleClose={() => toggleDialog(false)}
            /> */}

            <div style={{ fontSize: 15, marginBottom: 8 }}>
                {intl.formatMessage({ id: 'feedback_here' })}
                <Link
                    style={{
                        marginLeft: 6
                    }}
                    href='https://discord.gg/XtdZFBy4uR'
                    target='_blank'
                >
                    Connect with us on Discord
                </Link>
            </div>

        </div>
    );
}

const styles = {
    section_title: {
        fontWeight: '500',
        fontSize: 24,
        whiteSpace: 'nowrap',
        paddingTop: 34,
        paddingBottom: 12,
    },

    section_box: {
        padding: 14,
        boxShadow: '0px 4px 6px rgba(0, 0, 0, 0.2)',
        border: '1px solid #e3e3e3',
        borderRadius: 6,
    },
}
export default Feedbacks;
