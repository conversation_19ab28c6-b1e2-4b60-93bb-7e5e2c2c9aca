import { Button } from 'antd'
import { funblocks_domain } from '@/common/serverAPIUtil'
import browser from 'webextension-polyfill'
import { useIntl } from 'react-intl'
import mdit from 'markdown-it'

const md = mdit()

const AIFlow = () => {
  const intl = useIntl()
  const contextual_toolbar_img_path = browser.runtime.getURL(
    'assets/aiflow_benefits.png'
  )

  return (
    <div
      style={{
        width: '100%',
        alignItems: 'center',
        justifyContent: 'flex-start',
        padding: 10,
      }}
    >
      <div
        style={{
          paddingTop: 34,
          paddingBottom: 12,
          fontSize: 16,
        }}
      >
        {intl.formatMessage({ id: 'explore_to_aiflow_intro' })}
      </div>

      <div
        style={{
          marginTop: 20,
          padding: 18,
          boxShadow: '0px 4px 6px rgba(0, 0, 0, 0.2)',
          border: '1px solid #e3e3e3',
          borderRadius: 6,
        }}
      >
        <div
          style={{
            display: 'flex',
            flexDirection: 'row',
            justifyContent: 'space-between',
          }}
        >
          <span style={styles.title}>
            {intl.formatMessage({ id: 'what_is_aiflow' })}
          </span>
        </div>
        <div
          style={{
            marginTop: 6,
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'space-between',
            rowGap: 16,
          }}
        >
          <div
            className="transition-all duration-500"
            dangerouslySetInnerHTML={{
              __html: md.render(intl.formatMessage({ id: 'aiflow_intro' })),
            }}
          ></div>

          <div style={{ flex: 4 }}>
            <img
              className="highlighted-image"
              src={contextual_toolbar_img_path}
              draggable={false}
            />
          </div>
        </div>

        <div
          className="fill-available"
          style={{
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
            marginTop: 30,
          }}
        >
          <Button
            onClick={() => {
              window.open(`http://www.${funblocks_domain}/aiflow`)
            }}
          >
            {intl.formatMessage({ id: 'to_aiflow' })}
          </Button>
        </div>
      </div>
    </div>
  )
}

const styles = {
  item: {
    flexDirection: 'row',
    alignItems: 'center',
    display: 'flex',
    paddingTop: '36px',
    columnGap: '10px',
  },

  title: {
    fontWeight: 500,
    fontSize: 16,
    whiteSpace: 'nowrap',
  },
  input: {
    padding: '3px 12px',
    border: '1px solid #ccc',
    borderRadius: 15,
    outline: 'none',
    minWidth: '400px',
    fontSize: 14,
  },
  selector: {
    border: '1px solid #ccc',
    paddingLeft: 12,
    paddingRight: 6,
    borderRadius: 15,
    minWidth: 80,
  },
}

export default AIFlow
