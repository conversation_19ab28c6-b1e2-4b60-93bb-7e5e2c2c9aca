import { Button } from 'antd'
import { funblocks_domain } from '@/common/serverAPIUtil'
import browser from 'webextension-polyfill'
import { useIntl } from 'react-intl'

const prompts = () => {
  const intl = useIntl()
  const contextual_toolbar_img_path = browser.runtime.getURL(
    'assets/prompt_dev.png'
  )

  return (
    <div
      style={{
        width: '100%',
        alignItems: 'center',
        justifyContent: 'flex-start',
        padding: 10,
      }}
    >
      <div
        style={{
          marginTop: 56,
          padding: 18,
          boxShadow: '0px 4px 6px rgba(0, 0, 0, 0.2)',
          border: '1px solid #e3e3e3',
          borderRadius: 6,
        }}
      >
        <div
          style={{
            display: 'flex',
            flexDirection: 'row',
            justifyContent: 'space-between',
          }}
        >
          <span style={styles.title}>
            {intl.formatMessage({ id: 'what_is_prompt_app' })}
          </span>
        </div>
        <div
          style={{
            marginTop: 6,
            display: 'flex',
            flexDirection: 'row',
            justifyContent: 'space-between',
            columnGap: 6,
          }}
        >
          <div
            style={{
              fontSize: 14,
              color: '#333',
              display: 'flex',
              flexDirection: 'column',
              rowGap: 8,
              flex: 4,
            }}
          >
            {intl
              .formatMessage({ id: 'prompt_app_intro' })
              .split('\n')
              .map((str, index) => {
                return <span key={index}>{str}</span>
              })}
          </div>
          <div style={{ flex: 4 }}>
            <img
              className="highlighted-image"
              src={contextual_toolbar_img_path}
              draggable={false}
            />
          </div>
        </div>

        <div
          className="fill-available"
          style={{
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
            marginTop: 50,
          }}
        >
          <Button
            onClick={() => {
              window.open(
                `http://app.${funblocks_domain}/#/redirect?to=settings&page=prompts`
              )
            }}
          >
            {intl.formatMessage({ id: 'to_prompts_editor' })}
          </Button>
        </div>
      </div>
    </div>
  )
}

const styles = {
  item: {
    flexDirection: 'row',
    alignItems: 'center',
    display: 'flex',
    paddingTop: '36px',
    columnGap: '10px',
  },

  title: {
    fontWeight: 500,
    fontSize: 16,
    whiteSpace: 'nowrap',
  },
  input: {
    padding: '3px 12px',
    border: '1px solid #ccc',
    borderRadius: 15,
    outline: 'none',
    minWidth: '400px',
    fontSize: 14,
  },
  selector: {
    border: '1px solid #ccc',
    paddingLeft: 12,
    paddingRight: 6,
    borderRadius: 15,
    minWidth: 80,
  },
}

export default prompts
