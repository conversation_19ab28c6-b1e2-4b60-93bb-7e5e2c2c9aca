import { useCallback, useEffect, useState } from 'react'
import { useSWRConfig } from 'swr'
import { Settings } from '../types'
import { useSettings } from '../../common/store/settings'
import { IntlProvider, useIntl } from 'react-intl'
import messages from '@/common/messages'
import { Main } from './main'

const LNGS = [
  {
    label: 'English',
    value: 'en',
  },
  {
    label: '中文',
    value: 'cn',
  },
]

export const SettingsForm: React.FC = () => {
  const { loading, settings } = useSettings()
  const { mutate } = useSWRConfig()

  let lng = settings?.lang || 'en'

  useEffect(() => {
    if (!loading) {
      mutate('models')
    }
  }, [loading])

  if (loading) {
    return <div>loading...</div>
  }

  return (
    <IntlProvider locale={lng} messages={messages[lng]}>
      <Main />
    </IntlProvider>
  )
}

const styles = {
  selector: {
    border: '1px solid #ccc',
    paddingLeft: 12,
    paddingRight: 6,
    borderRadius: 15,
  },
}
