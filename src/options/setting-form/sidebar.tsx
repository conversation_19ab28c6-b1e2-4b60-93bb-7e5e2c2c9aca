import { useIntl } from 'react-intl'
import { Selector } from '@/common/selector'
import { useSettings } from '@/common/store/settings'
import { Switch } from '@mui/material'
import { Radio, Space } from 'antd'
import { side_bar_widget_id } from '@/content/container/slidebar-menu'
import {
  is_widget_switched,
  switch_widget,
} from '@/content/container/slidebar-menu/quick-settings'
import DisabledPages from './disabled_pages'
import browser from 'webextension-polyfill'
import { getCurrentProduct } from '@/common/product-features'

const SideBar = () => {
  const intl = useIntl()
  const { settings = {}, setSettings } = useSettings()
  const sidebar_img_path = browser.runtime.getURL('assets/sidebar.png')
  // console.log('settings...........', settings)

  return (
    <div
      style={{
        width: '100%',
        alignItems: 'center',
        justifyContent: 'flex-start',
        padding: 10,
      }}
    >
      <div style={styles.item}>
        <span style={styles.title}>
          {intl.formatMessage({ id: 'settings_side_bar' })}
        </span>
        <Switch
          size="small"
          checked={is_widget_switched(side_bar_widget_id, settings)}
          onChange={(event) =>
            switch_widget(
              side_bar_widget_id,
              event.target.checked,
              settings,
              setSettings
            )
          }
        />
      </div>

      <div style={{ ...styles.item, paddingTop: 6 }}>
        <span
          style={{
            fontSize: 15,
            color: '#555',
          }}
        >
          {intl.formatMessage({ id: 'settings_side_bar_desc' })}
        </span>
      </div>

      <div style={styles.item}>
        <span style={styles.title}>
          {intl.formatMessage({ id: 'settings_side_bar_align' })}
        </span>
      </div>
      <Radio.Group
        style={{ marginTop: 6 }}
        onChange={(e) => {
          setSettings({
            ...settings,
            sidebar: {
              ...settings.sidebar,
              align: e.target.value,
            },
          })
        }}
        value={settings?.sidebar?.align || 'right'}
      >
        <Space direction="horizontal">
          <Radio value={'left'}>
            {intl.formatMessage({ id: 'left_side' })}
          </Radio>
          <Radio value={'right'}>
            {intl.formatMessage({ id: 'right_side' })}
          </Radio>
        </Space>
      </Radio.Group>

      <DisabledPages
        widgetId={side_bar_widget_id}
        widgetLabel={intl.formatMessage({ id: 'settings_side_bar' })}
      />

      {!['mindmap'].includes(getCurrentProduct()) && (
        <div
          style={{
            marginTop: 56,
            padding: 18,
            boxShadow: '0px 4px 6px rgba(0, 0, 0, 0.2)',
            border: '1px solid #e3e3e3',
            borderRadius: 6,
          }}
        >
          <div
            style={{
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'space-between',
            }}
          >
            <span style={styles.title}>
              {intl.formatMessage({ id: 'what_is_sidebar' })}
            </span>
          </div>
          <div
            style={{
              marginTop: 6,
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'space-between',
              columnGap: 6,
            }}
          >
            <div
              style={{
                fontSize: 14,
                color: '#333',
                display: 'flex',
                flexDirection: 'column',
                rowGap: 8,
                flex: 4,
              }}
            >
              {intl
                .formatMessage({ id: 'sidebar_intro' })
                .split('\n')
                .map((str) => {
                  return <span>{str}</span>
                })}
            </div>
            <div style={{ flex: 6 }}>
              <img
                className="highlighted-image"
                width={420}
                src={sidebar_img_path}
                draggable={false}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

const styles = {
  item: {
    flexDirection: 'row',
    alignItems: 'center',
    display: 'flex',
    paddingTop: '36px',
    columnGap: '10px',
  },

  title: {
    fontWeight: 500,
    fontSize: 16,
    whiteSpace: 'nowrap',
  },
  input: {
    padding: '3px 12px',
    border: '1px solid #ccc',
    borderRadius: 15,
    outline: 'none',
    minWidth: '400px',
    fontSize: 14,
  },
  selector: {
    border: '1px solid #ccc',
    paddingLeft: 12,
    paddingRight: 6,
    borderRadius: 15,
    minWidth: 80,
  },
}

export default SideBar
