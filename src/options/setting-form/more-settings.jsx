import { useDispatch, useSelector } from "react-redux";
import { useIntl } from "react-intl";
import { useSettings } from "@/common/store/settings";
import { Checkbox } from "antd";
import { Selector } from "@/common/selector";
import { getCurrentProduct } from "@/common/product-features";

const LNGS = [
    {
        value: 'en',
        label: 'English',
    },
    {
        value: 'cn',
        label: '中文',
    },
    // {
    //   value: 'de',
    //   label: 'Deutsch',
    // },
    // {
    //   value: 'jp',
    //   label: '日本語',
    // },
]

const MoreSettings = () => {
    const intl = useIntl();
    const { loading, settings, setSettings, refresh } = useSettings()
    const lng_list = useSelector((state) => state.uiState.lng_list)

    return (
        <div style={{ width: '100%', alignItems: 'center', justifyContent: 'flex-start', padding: 10, paddingTop: 24 }}>
            <div style={styles.section_title}>
                {intl.formatMessage({ id: 'settings_language_menu' })}
            </div>
            <div style={styles.section_box}>
                <div style={styles.item}>
                    <div style={styles.title}>
                        {intl.formatMessage({ id: 'settings_language_desc' }) + ':'}
                    </div>

                    <Selector
                        inputStyle={styles.selector}
                        onChange={(value) => {
                            setSettings({
                                ...settings,
                                lang: value,
                            })
                        }}
                        value={settings.lang}
                        options={LNGS}
                    />
                </div>

                {lng_list && (
                    <>
                        <div style={styles.item}>
                            <div style={styles.title}>
                                {intl.formatMessage({ id: 'settings_ai_respond' }) + ':'}
                            </div>

                            <Selector
                                options={lng_list
                                    .filter((lng) => !!lng)
                                    .map((lang) => {
                                        return { label: lang.label, value: lang.Symbol }
                                    })}
                                value={
                                    settings.ai_respond_lng == 'cn'
                                        ? 'zh'
                                        : settings.ai_respond_lng
                                }
                                onChange={(value) => {
                                    setSettings({
                                        ...settings,
                                        ai_respond_lng: value,
                                    })
                                }}
                                inputStyle={styles.selector}
                            />
                        </div>
                        <div
                            style={{
                                marginTop: 8,
                                fontSize: 14,
                                color: 'gray',
                            }}
                        >
                            {intl.formatMessage({ id: 'settings_ai_respond_desc' })}
                        </div>
                    </>
                )}
            </div>

            {
                !['mindmap'].includes(getCurrentProduct()) &&
                <div style={{ ...styles.item, marginTop: 30 }}>
                    <Checkbox
                        checked={settings.give_up_ai_content_directly}
                        onChange={(event) => setSettings({
                            ...settings,
                            give_up_ai_content_directly: event.target.checked
                        })}
                    >{intl.formatMessage({ id: 'close_ai_modal_confirm' })}</Checkbox>
                </div>
            }
        </div>
    );
}

const styles = {
    item: {
        flexDirection: 'row',
        alignItems: 'center',
        display: 'flex',
        paddingTop: '10px',
        columnGap: '10px',
    },

    title: {
        fontWeight: 500,
        fontSize: 16,
        whiteSpace: 'nowrap',
    },

    section_title: {
        fontWeight: '500',
        fontSize: 24,
        whiteSpace: 'nowrap',
        paddingTop: 34,
        paddingBottom: 12,
    },

    section_box: {
        padding: 14,
        boxShadow: '0px 4px 6px rgba(0, 0, 0, 0.2)',
        border: '1px solid #e3e3e3',
        borderRadius: 6,
    },

    input: {
        padding: '3px 12px',
        border: '1px solid #ccc',
        borderRadius: 15,
        outline: 'none',
        minWidth: '400px',
        fontSize: 14,
    },
    selector: {
        border: '1px solid #ccc',
        paddingLeft: 12,
        paddingRight: 6,
        borderRadius: 15,
        minWidth: 80,
    },
}

export default MoreSettings;