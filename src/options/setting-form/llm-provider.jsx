import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useIntl } from "react-intl";
import { Button } from 'antd'
import { LLM_API_KEYS, LLM_API_KEY_MODAL } from "@/common/constants/actionTypes";
import { EditableMenuItemActions } from "./editable-menu-item-actions";
import { useSettings } from "@/common/store/settings";
import ModelSelector from "@/content/container/ask-funblocks/content/model-selector";

const LlmProvider = () => {
    const intl = useIntl();
    const dispatch = useDispatch();
    const { settings, setSettings } = useSettings();
    const llm_api_keys = settings?.llm_api_keys;

    return (
        <div style={{ width: '100%', alignItems: 'center', justifyContent: 'flex-start', padding: 10, paddingTop: 24 }}>

            <div
                style={styles.section_box}
            >
                <div style={styles.item}>
                    <div style={styles.title}>
                        {intl.formatMessage({ id: 'set_default_ai_model' }) + ':'}
                    </div>

                    <ModelSelector trigger_key_model={true}/>
                </div>

                    <div style={{...styles.title, marginTop: 20}}>
                        {intl.formatMessage({ id: 'private_llm_key_list' }) + ':'}
                    </div>
                <div
                    style={{
                        display: 'flex',
                        flexDirection: 'column',
                        justifyContent: 'center',
                        padding: 20,
                        backgroundColor: '#f3f3f3',
                        borderRadius: 10,
                        marginTop: 10,
                    }}
                >
                    <div
                        style={{
                            display: 'flex',
                            flexDirection: 'row',
                            alignItems: 'center',
                            fontWeight: 500,
                            fontSize: 16,
                        }}
                    >
                        <div style={{ flex: 3 }}>
                            {intl.formatMessage({ id: 'name' })}
                        </div>
                        <div style={{ flex: 2 }}>
                            Provider
                        </div>
                        <div style={{ flex: 3 }}>
                            Model
                        </div>
                        <div style={{ flex: 1, display: 'flex', flexDirection: 'row', justifyContent: 'flex-end' }}>
                            Actions
                        </div>
                    </div>

                    {llm_api_keys?.map((item, index) => {
                        return (
                            <div
                                key={index + ''}
                            >
                                <div
                                    style={{
                                        height: 1,
                                        backgroundColor: '#ccc',
                                        marginTop: 8,
                                    }}
                                />
                                <div
                                    style={{
                                        display: 'flex',
                                        flexDirection: 'row',
                                        alignItems: 'center',
                                        fontSize: 14,
                                        paddingTop: 8,
                                        color: '#444',
                                    }}
                                >
                                    <div style={{ flex: 3, fontWeight: 500, fontSize: 15 }}>
                                        {item.name}
                                    </div>
                                    <div style={{ flex: 2 }}>{item.provider}</div>
                                    <div style={{ flex: 3 }}>
                                        {item.model}
                                    </div>
                                    <div style={{ flex: 1 }}>
                                        <EditableMenuItemActions
                                            item={item}
                                            onDelete={(item) => {
                                                let keys = [...llm_api_keys];

                                                setSettings({
                                                    ...(settings || {}),
                                                    llm_api_keys: keys.filter(it => it.id != item.id)
                                                })
                                            }}
                                            onEdit={(item) => {
                                                dispatch({
                                                    type: LLM_API_KEY_MODAL,
                                                    value: {
                                                        visible: true,
                                                        item
                                                    }
                                                })
                                            }}
                                        />
                                    </div>
                                </div>
                            </div>
                        )
                    })}
                </div>

                <div
                    className="fill-available"
                    style={{
                        display: 'flex',
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'center',
                        marginTop: 20,
                    }}
                >
                    <Button
                        onClick={() => {
                            dispatch({
                                type: LLM_API_KEY_MODAL,
                                value: {
                                    visible: true
                                }
                            })
                        }}
                    >
                        {intl.formatMessage({ id: 'add_private_llm_key' })}
                    </Button>
                </div>
            </div>
        </div>
    );
}

const styles = {

    item: {
        flexDirection: 'row',
        alignItems: 'center',
        display: 'flex',
        paddingTop: '10px',
        columnGap: '10px',
    },

    title: {
        fontWeight: 500,
        fontSize: 16,
        whiteSpace: 'nowrap',
    },

    section_title: {
        fontWeight: '500',
        fontSize: 24,
        whiteSpace: 'nowrap',
        paddingTop: 34,
        paddingBottom: 12,
    },

    section_box: {
        padding: 14,
        boxShadow: '0px 4px 6px rgba(0, 0, 0, 0.2)',
        border: '1px solid #e3e3e3',
        borderRadius: 6,
    },

    input: {
        padding: '3px 12px',
        border: '1px solid #ccc',
        borderRadius: 15,
        outline: 'none',
        minWidth: '400px',
        fontSize: 14,
    },
    selector: {
        border: '1px solid #ccc',
        paddingLeft: 12,
        paddingRight: 6,
        borderRadius: 15,
        minWidth: 80,
    },
}

export default LlmProvider;