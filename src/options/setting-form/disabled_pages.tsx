import { useIntl } from 'react-intl'
import { useSettings } from '@/common/store/settings'
import { Trash } from '@styled-icons/bootstrap/Trash'
import { cloneDeep } from 'lodash-es'

export const Triggers = ['/', '//', '\\', '\\\\', 'none']

const DisabledPages = ({ widgetId, widgetLabel }) => {
  const intl = useIntl()
  const { settings = {}, setSettings } = useSettings()
  // console.log('settings...........', settings)

  if (!settings?.widget_disabled_range[widgetId]?.pages?.length) {
    return null
  }

  return (
    <>
      <div style={styles.item}>
        <span style={styles.title}>
          {intl.formatMessage(
            { id: 'disabled_pages' },
            { widget: widgetLabel }
          )}
        </span>
      </div>
      <div className="grid" style={{ marginTop: 10 }}>
        {settings?.widget_disabled_range[widgetId]?.pages?.map(
          (page, index) => {
            return (
              <div
                key={index + ''}
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  padding: 4,
                  paddingLeft: 10,
                  paddingRight: 10,
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  border: '1px solid #eee',
                }}
              >
                <div
                  style={{
                    fontSize: 14,
                    color: '#333',
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                  }}
                >
                  {page}
                </div>
                <div
                  style={{ cursor: 'pointer' }}
                  onClick={() => {
                    let widget_disabled_range = cloneDeep(
                      settings.widget_disabled_range
                    )
                    widget_disabled_range[widgetId].pages =
                      widget_disabled_range[widgetId].pages.filter(
                        (p) => page != p
                      )

                    setSettings({
                      ...settings,
                      widget_disabled_range,
                    })
                  }}
                >
                  <Trash size={18} />
                </div>
              </div>
            )
          }
        )}
      </div>
    </>
  )
}

const styles = {
  item: {
    flexDirection: 'row',
    alignItems: 'center',
    display: 'flex',
    paddingTop: '36px',
    columnGap: '10px',
  },
  title: {
    fontWeight: 500,
    fontSize: 16,
    whiteSpace: 'nowrap',
  },
  input: {
    padding: '3px 12px',
    border: '1px solid #ccc',
    borderRadius: 15,
    outline: 'none',
    minWidth: '400px',
    fontSize: 14,
  },
  selector: {
    border: '1px solid #ccc',
    paddingLeft: 12,
    paddingRight: 6,
    borderRadius: 15,
    minWidth: 80,
  },
}

export default DisabledPages
