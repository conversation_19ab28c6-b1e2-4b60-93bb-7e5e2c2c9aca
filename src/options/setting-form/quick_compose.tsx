import { useIntl } from 'react-intl'
import { Selector } from '@/common/selector'
import { useSettings } from '@/common/store/settings'
import { Switch } from '@mui/material'
import {
  is_widget_switched,
  switch_widget,
} from '@/content/container/slidebar-menu/quick-settings'
import { writerButtonElementId } from '@/content/container/ask-funblocks/widgets/ai-edit-button'
import DisabledPages from './disabled_pages'
import browser from 'webextension-polyfill'

export const Triggers = ['/', '//', '\\', '\\\\', 'none']

const QuickCompose = () => {
  const intl = useIntl()
  const { settings = {}, setSettings } = useSettings()
  // console.log('settings...........', settings)
  const writer_assistant_img_path = browser.runtime.getURL(
    'assets/ai_writing_assistant.png'
  )
  const icon_gmail_img_path = browser.runtime.getURL('assets/gmail.png')
  const icon_notion_img_path = browser.runtime.getURL('assets/notion.png')
  const icon_outlook_img_path = browser.runtime.getURL('assets/outlook.png')
  const icon_facebook_img_path = browser.runtime.getURL('assets/facebook.webp')
  // const icon_word_img_path = browser.runtime.getURL('assets/word.png');
  // const icon_reddit_img_path = browser.runtime.getURL('assets/reddit.webp');
  // const icon_quora_img_path = browser.runtime.getURL('assets/quora.png');

  return (
    <div
      style={{
        width: '100%',
        alignItems: 'center',
        justifyContent: 'flex-start',
        padding: 10,
      }}
    >
      <div style={styles.item}>
        <span style={styles.title}>
          {intl.formatMessage({ id: 'settings_ai_trigger' })}
        </span>
        <Selector
          inputStyle={{
            ...styles.selector,
            paddingLeft: 16,
          }}
          onChange={(value) => {
            setSettings({
              ...settings,
              trigger: value,
            })
          }}
          value={settings.trigger}
          options={Triggers.map((t) => ({
            label: t != 'none' ? t : intl.formatMessage({ id: 'none' }),
            value: t,
          }))}
        />
        {settings.trigger?.includes('/') && (
          <span>
            {intl.formatMessage({ id: 'settings_ai_trigger_conflict_warn' })}
          </span>
        )}
      </div>
      <div style={{ ...styles.item, paddingTop: 6 }}>
        <span
          style={{
            fontSize: 15,
            color: '#555',
          }}
        >
          {intl.formatMessage(
            {
              id:
                settings?.trigger && settings?.trigger !== 'none'
                  ? 'settings_ai_trigger_desc'
                  : 'settings_ai_trigger_none_desc',
            },
            { trigger: "'" + settings.trigger + "'" }
          )}
        </span>
      </div>

      <div style={styles.item}>
        <span style={styles.title}>
          {intl.formatMessage({ id: 'settings_quick_compose' })}
        </span>
        <Switch
          size="small"
          checked={is_widget_switched(writerButtonElementId, settings)}
          onChange={(event) =>
            switch_widget(
              writerButtonElementId,
              event.target.checked,
              settings,
              setSettings
            )
          }
        />
      </div>

      <div style={{ ...styles.item, paddingTop: 6 }}>
        <span
          style={{
            fontSize: 15,
            color: '#555',
          }}
        >
          {intl.formatMessage({ id: 'settings_quick_compose_desc' })}
        </span>
      </div>

      <DisabledPages
        widgetId={writerButtonElementId}
        widgetLabel={intl.formatMessage({ id: 'settings_quick_compose' })}
      />

      <div
        style={{
          marginTop: 56,
          padding: 18,
          boxShadow: '0px 4px 6px rgba(0, 0, 0, 0.2)',
          border: '1px solid #e3e3e3',
          borderRadius: 6,
        }}
      >
        <div
          style={{
            display: 'flex',
            flexDirection: 'row',
            justifyContent: 'space-between',
          }}
        >
          <span style={styles.title}>
            {intl.formatMessage({ id: 'what_is_writing_assistant' })}
          </span>
          <div
            style={{
              display: 'flex',
              flexDirection: 'row',
              alignItems: 'center',
            }}
          >
            <img width={32} src={icon_gmail_img_path} draggable={false} />
            <img width={32} src={icon_outlook_img_path} draggable={false} />
            <img width={32} src={icon_notion_img_path} draggable={false} />
            <img width={32} src={icon_facebook_img_path} draggable={false} />
            <span style={{ paddingLeft: 3, fontSize: 22 }}>...</span>
          </div>
        </div>
        <div
          style={{
            marginTop: 6,
            display: 'flex',
            flexDirection: 'row',
            justifyContent: 'space-between',
            columnGap: 6,
          }}
        >
          <div
            style={{
              fontSize: 14,
              color: '#333',
              display: 'flex',
              flexDirection: 'column',
              rowGap: 8,
              flex: 5,
            }}
          >
            {intl
              .formatMessage({ id: 'writing_assistant_intro' })
              .split('\n')
              .map((str) => {
                return <span>{str}</span>
              })}
          </div>
          <div style={{ flex: 6 }}>
            <img
              className="highlighted-image"
              width={420}
              src={writer_assistant_img_path}
              draggable={false}
            />
          </div>
        </div>
      </div>
    </div>
  )
}

const styles = {
  item: {
    flexDirection: 'row',
    alignItems: 'center',
    display: 'flex',
    paddingTop: '36px',
    columnGap: '10px',
  },

  title: {
    fontWeight: 500,
    fontSize: 16,
    whiteSpace: 'nowrap',
  },
  input: {
    padding: '3px 12px',
    border: '1px solid #ccc',
    borderRadius: 15,
    outline: 'none',
    minWidth: '400px',
    fontSize: 14,
  },
  selector: {
    border: '1px solid #ccc',
    paddingLeft: 12,
    paddingRight: 6,
    borderRadius: 15,
    minWidth: 80,
  },
}

export default QuickCompose
