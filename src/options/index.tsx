import { createRoot } from 'react-dom/client'
import 'antd/dist/reset.css'
import './index.css'
import '../common/i18n'
import { App } from './app'
import { initI18n } from '../common/i18n'
import { Provider } from 'react-redux'
import getStoreConfig from '../common/local_store'
import { PersistGate } from 'redux-persist/integration/react'

const { store, persistor } = getStoreConfig()

const render = async () => {
  await initI18n()
  createRoot(document.getElementById('app')).render(
    <Provider store={store}>
      <PersistGate loading={null} persistor={persistor}>
        <App />
      </PersistGate>
    </Provider>
  )
}

render()
