#!/usr/bin/env node

// Test script to verify product feature configurations

const { productConfigs, isFeatureEnabled } = require('./product-configs.js')

console.log('🧪 Testing Product Feature Configurations\n')

// Test both products
const products = ['funblocks', 'mindmap']

products.forEach((product) => {
  console.log(`📦 Testing ${product.toUpperCase()} product:`)
  console.log(`   Name: ${productConfigs[product].manifestName}`)
  console.log(`   Version: ${productConfigs[product].version}`)
  console.log(`   Icon: ${productConfigs[product].icon}`)

  // Set environment for testing
  process.env.PRODUCT = product

  // Test sidebar features
  console.log('\n   🔧 Sidebar Features:')
  console.log(
    `   - ai_flow_question: ${isFeatureEnabled('sidebarMenu.ai_flow_question')}`
  )
  console.log(
    `   - ai_flow_explore: ${isFeatureEnabled('sidebarMenu.ai_flow_explore')}`
  )
  console.log(
    `   - assistant_with_page_context: ${isFeatureEnabled(
      'sidebarMenu.assistant_with_page_context'
    )}`
  )
  console.log(
    `   - quick_settings: ${isFeatureEnabled('sidebarMenu.quick_settings')}`
  )
  console.log(
    `   - screen_capture: ${isFeatureEnabled('sidebarMenu.screen_capture')}`
  )

  // Test widget features
  console.log('\n   🎛️  Widget Features:')
  console.log(
    `   - youtube widget: ${isFeatureEnabled('widgets.youtube.enabled')}`
  )
  console.log(
    `   - youtube summary button: ${isFeatureEnabled(
      'widgets.youtube.showSummaryButton'
    )}`
  )
  console.log(`   - reply widget: ${isFeatureEnabled('widgets.reply')}`)
  console.log(`   - ai writer widget: ${isFeatureEnabled('widgets.aiWriter')}`)
  console.log(
    `   - codes explain widget: ${isFeatureEnabled('widgets.codesExplain')}`
  )

  console.log('\n' + '='.repeat(50) + '\n')
})

// Verify expected differences
console.log('✅ Verification Summary:')

// Set to mindmap for comparison
process.env.PRODUCT = 'mindmap'

const mindmapDisabledFeatures = [
  'sidebarMenu.quick_settings',
  'sidebarMenu.screen_capture',
  'widgets.youtube.showSummaryButton',
  'widgets.reply',
  'widgets.aiWriter',
  'widgets.codesExplain',
]

const allDisabled = mindmapDisabledFeatures.every(
  (feature) => !isFeatureEnabled(feature)
)

if (allDisabled) {
  console.log('✅ AI MindMap correctly disables expected features')
} else {
  console.log('❌ AI MindMap feature configuration error')
  mindmapDisabledFeatures.forEach((feature) => {
    if (isFeatureEnabled(feature)) {
      console.log(`   - ${feature} should be disabled but is enabled`)
    }
  })
}

// Set to funblocks for comparison
process.env.PRODUCT = 'funblocks'

const funblocksEnabledFeatures = [
  'sidebarMenu.ai_flow_question',
  'sidebarMenu.ai_flow_explore',
  'sidebarMenu.assistant_with_page_context',
  'sidebarMenu.quick_settings',
  'sidebarMenu.screen_capture',
  'widgets.youtube.enabled',
  'widgets.youtube.showSummaryButton',
  'widgets.reply',
  'widgets.aiWriter',
  'widgets.codesExplain',
]

const allEnabled = funblocksEnabledFeatures.every((feature) =>
  isFeatureEnabled(feature)
)

if (allEnabled) {
  console.log('✅ FunBlocks correctly enables all features')
} else {
  console.log('❌ FunBlocks feature configuration error')
  funblocksEnabledFeatures.forEach((feature) => {
    if (!isFeatureEnabled(feature)) {
      console.log(`   - ${feature} should be enabled but is disabled`)
    }
  })
}

console.log('\n🎉 Product feature testing completed!')
