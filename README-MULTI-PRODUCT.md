# Multi-Product Build System

This project supports building two different Chrome Extension products from the same codebase:

## Products

### 1. FunBlocks AI Extension (Original Product)

- **Name**: FunBlocks AI - Your Ultimate Writing and Reading Copilot
- **Features**: All features enabled
- **Icon**: `icon.png`
- **Output**: `dist-funblocks/`

### 2. AI MindMap Extension (New Product)

- **Name**: AI MindMap - Smart Mind Mapping Assistant
- **Features**: Limited feature set for mind mapping focus
- **Icon**: `mindmap-icon.png`
- **Output**: `dist-mindmap/`

## Feature Differences

### AI MindMap Extension Limitations:

- **Sidebar Menu**: Only shows `ai_flow_question`, `ai_flow_explore`, `assistant_with_page_context`
- **Widgets**: Only YouTube widget (with summary button hidden)
- **Hidden Features**: Quick settings, screen capture, reply widget, AI writer widget, codes explain widget

## Build Commands

### Development Mode

```bash
# Default development (FunBlocks)
npm run dev

# Develop FunBlocks AI Extension
npm run dev:funblocks

# Develop AI MindMap Extension
npm run dev:mindmap
```

### Individual Product Builds

```bash
# Build FunBlocks AI Extension only
npm run build:funblocks

# Build AI MindMap Extension only
npm run build:mindmap

# Build both products
npm run build:all
```

### Complete Multi-Product Build

```bash
# Build and package both products
./build-multi-product.sh
```

This will create:

- `release/funblocks-ai-extension.zip`
- `release/ai-mindmap-extension.zip`

### Legacy Build (FunBlocks only)

```bash
# Original build script (builds FunBlocks by default)
./build.sh
```

## Configuration Files

### Product Configuration

- `product-configs.js` - Defines features for each product
- `manifest.json` - FunBlocks manifest
- `manifest-mindmap.json` - AI MindMap manifest

### Build Configuration

- `tsup.config.ts` - Updated to support multi-product builds
- `package.json` - Added new build scripts

## Feature Control System

The system uses environment variables and feature flags to control which features are enabled:

### Environment Variables

- `PRODUCT=funblocks` - Build FunBlocks AI Extension
- `PRODUCT=mindmap` - Build AI MindMap Extension

### Feature Detection

```javascript
import { ProductFeatures } from '@/common/product-features'

// Check if a feature is enabled
if (ProductFeatures.isSidebarFeatureEnabled('quick_settings')) {
  // Show quick settings
}

if (ProductFeatures.isYoutubeSummaryButtonEnabled()) {
  // Show YouTube summary button
}
```

## Development

### Adding New Products

1. Add product configuration to `product-configs.js`
2. Create new manifest file (e.g., `manifest-newproduct.json`)
3. Add build script to `package.json`
4. Update `build-multi-product.sh`

### Adding Feature Flags

1. Update product configurations in `product-configs.js`
2. Add feature checks in relevant components using `ProductFeatures`

## File Structure

```
├── product-configs.js          # Product feature configurations
├── manifest.json              # FunBlocks manifest
├── manifest-mindmap.json      # AI MindMap manifest
├── build-multi-product.sh     # Multi-product build script
├── src/
│   └── common/
│       └── product-features.ts # Feature detection utilities
├── dist-funblocks/            # FunBlocks build output
├── dist-mindmap/              # AI MindMap build output
└── release/                   # Packaged extensions
    ├── funblocks-ai-extension.zip
    └── ai-mindmap-extension.zip
```

## Product Isolation

### Conflict Prevention

- **Unique IDs**: Each product uses product-specific element IDs (e.g., `funblocks-widget-id` vs `mindmap-widget-id`)
- **Shadow DOM**: Different container tags (`<funblocks-container>` vs `<mindmap-container>`)
- **CSS Classes**: Product-specific class names prevent style conflicts
- **Storage**: Separate storage namespaces for each product

### Testing

```bash
# Test feature configurations
npm run test:features

# Test product isolation
npm run test:isolation
```

## Notes

- Both products can be installed simultaneously without conflicts
- Each product uses unique DOM element IDs and CSS classes
- Feature control is handled at build time and runtime
- Icons and manifests are automatically copied based on the product being built
- The system is extensible for adding more products in the future
