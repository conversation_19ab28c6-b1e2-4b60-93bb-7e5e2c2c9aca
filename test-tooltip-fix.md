# Tooltip 和 Selector 组件修复总结

## 问题描述

ai-form 中的 Tooltip 和 selector 组件没有正确工作，当显示 Tooltip 或下拉菜单时，内容都显示在 ai-form 下方，将整个区域撑长。

## 根本原因

1. **Shadow DOM 隔离问题**: antd 组件的弹出层（Tooltip、Popover）默认渲染到 document.body，但在 Shadow DOM 环境中无法正确显示
2. **样式隔离问题**: antd 组件的 CSS 样式没有正确应用到 Shadow DOM 中
3. **z-index 层级问题**: 弹出层的 z-index 不够高，被其他元素遮盖

## 解决方案

### 1. 创建 Shadow DOM 容器工具函数

创建了 `src/common/shadow-dom-utils.ts`，提供 `getShadowDOMContainer` 函数：

- 自动查找 Shadow DOM 容器
- 设置足够高的 z-index (**********)
- 确保弹出层正确渲染在 Shadow DOM 内部

### 2. 修改 antd 组件配置

为所有 Tooltip 和 Popover 组件添加 `getPopupContainer={getShadowDOMContainer}` 属性：

- `src/common/selector.jsx` - Selector 组件中的 Popover 和 Tooltip
- `src/content/container/ask-funblocks/content/ai-form.jsx` - AI 表单中的 Tooltip 和 Popover
- `src/content/container/slidebar-menu/index.jsx` - 侧边栏菜单中的 Tooltip
- `src/content/container/ask-funblocks/content/index.tsx` - 内容区域中的 Tooltip

### 3. 增强 StyleProvider 配置

修改 `src/content/index.tsx` 中的 CustomCache 类：

- 自动为 antd 组件样式添加高 z-index
- 确保样式正确应用到 Shadow DOM 中
- 设置 StyleProvider 的 container 为 shadowRoot

### 4. 添加 CSS 样式修复

在 `src/content/index.css` 中添加：

- `.ant-tooltip { z-index: ********** !important; }`
- `.ant-popover { z-index: ********** !important; }`
- `.ant-select-dropdown { z-index: ********** !important; }`
- 其他相关样式的 z-index 修复

## 修改的文件列表

1. `src/common/shadow-dom-utils.ts` - 新建工具函数
2. `src/common/selector.jsx` - 修复 Selector 组件
3. `src/content/container/ask-funblocks/content/ai-form.jsx` - 修复 AI 表单
4. `src/content/container/slidebar-menu/index.jsx` - 修复侧边栏
5. `src/content/container/ask-funblocks/content/index.tsx` - 修复内容区域
6. `src/content/container/ask-funblocks/content/model-selector.jsx` - 修复模型选择器
7. `src/content/index.tsx` - 增强 StyleProvider
8. `src/content/index.css` - 添加样式修复

## 测试验证

修复后应该验证：

1. Tooltip 正确显示在触发元素附近，不被遮盖
2. Selector 下拉菜单正确显示，不撑长容器
3. 弹出层有足够高的 z-index，不被其他元素遮盖
4. 在不同页面和场景下都能正常工作

## 技术细节

### getShadowDOMContainer 函数工作原理

1. 从触发元素开始向上遍历 DOM 树
2. 查找 Shadow DOM 根节点（nodeType === Node.DOCUMENT_FRAGMENT_NODE）
3. 在 Shadow DOM 中查找合适的容器元素
4. 设置容器的 z-index 为最大值，确保弹出层不被遮盖
5. 如果没有找到 Shadow DOM，回退到 document.body

### CustomCache 增强

- 自动检测 antd 组件样式（.ant-tooltip, .ant-popover, .ant-select-dropdown）
- 为这些样式自动添加高 z-index 值
- 确保样式正确注入到 Shadow DOM 中

### CSS 样式修复

- 为所有 antd 弹出层组件添加 `!important` 的高 z-index
- 确保在各种复杂布局中都能正确显示

## 注意事项

- 所有 antd 组件的弹出层都需要使用 `getShadowDOMContainer`
- z-index 值 ********** 是 CSS 中的最大安全整数值
- 修复后的组件在非 Shadow DOM 环境中也能正常工作
- 构建测试通过，没有引入新的错误

## 新发现的问题和修复

### Quick Settings Modal 无法弹出问题
**问题描述**: 在修复 antd 样式问题之后，发现 quick-settings 的 modal 无法弹出，但在 ai-form 显示的时候，quick-settings 可以弹出。

**根本原因**: Modal 组件也需要配置 `getContainer` 属性来指定渲染容器，否则会渲染到 document.body，在 Shadow DOM 环境中无法正确显示。

**修复方案**:
1. 为所有 Modal 组件添加 `getContainer` 配置
2. 添加 Modal 相关的 CSS z-index 样式
3. 更新 CustomCache 类处理 Modal 样式

**修复的 Modal 组件**:
- `src/content/container/slidebar-menu/quick-settings.jsx` - QuickSettings Modal
- `src/content/container/ask-funblocks/widgets/close-widget-modal.jsx` - CloseWidget Modal
- `src/options/setting-form/llm-api-key-modal.jsx` - LlmAPIKey Modal

**添加的 CSS 样式**:
```css
.ant-modal-root { z-index: ********** !important; }
.ant-modal-wrap { z-index: ********** !important; }
.ant-modal { z-index: ********** !important; }
.ant-modal-mask { z-index: 2147483646 !important; }
```

## 状态

✅ 修复完成，构建成功
✅ Modal 弹出问题已修复
