// tsup.config.ts
import { defineConfig } from "tsup";
import { copy } from "esbuild-plugin-copy";
import { NodeModulesPolyfillPlugin } from "@esbuild-plugins/node-modules-polyfill";
var tag = "funblocks-container";
var tsup_config_default = defineConfig({
  entry: [
    "./src/content/index.tsx",
    "./src/options/index.tsx",
    "./src/popup/index.tsx",
    "./src/background/index.ts"
  ],
  target: "chrome112",
  format: "cjs",
  splitting: false,
  sourcemap: false,
  clean: true,
  minify: true,
  define: {
    "process.env.NODE_ENV": JSON.stringify(process.env.NODE_ENV),
    "import.meta.url": JSON.stringify("chrome-extension://undefined/"),
    "import.meta.env": JSON.stringify({}),
    "import.meta": JSON.stringify({ url: "chrome-extension://undefined/", env: {} })
  },
  injectStyle: (css) => {
    return `
        var setFunBlocksStyle = function() {
          var style = document.createElement('style');
          style.type = 'text/css';
          style.innerHTML = ${css};
          if (!chrome.runtime.openOptionsPage) {
            setTimeout(() => {
              try {
                var root = document.getElementsByTagName('${tag}')[0].shadowRoot;
                root.appendChild(style);    
              } catch {
                setFunBlocksStyle()
              }
            }, 100)
          } else {
            var root = document.head || document.getElementsByTagName('head')[0];
            root.appendChild(style)
          }
         
        };
        setFunBlocksStyle();
      `;
  },
  outExtension: () => ({ js: ".js" }),
  esbuildPlugins: [
    NodeModulesPolyfillPlugin(),
    copy({
      assets: [
        {
          from: ["./src/options/index.html"],
          to: ["./options"]
        },
        {
          from: ["./src/popup/index.html"],
          to: ["./popup"]
        },
        {
          from: ["./node_modules/animate.css/animate.css"],
          to: ["./content"]
        },
        {
          from: ["./src/assets/*"],
          to: ["./assets"]
        }
      ],
      watch: process.env.NODE_ENV !== "production"
    })
  ]
});
export {
  tsup_config_default as default
};
//# sourceMappingURL=data:application/json;base64,ewogICJ2ZXJzaW9uIjogMywKICAic291cmNlcyI6IFsidHN1cC5jb25maWcudHMiXSwKICAic291cmNlc0NvbnRlbnQiOiBbImNvbnN0IF9faW5qZWN0ZWRfZmlsZW5hbWVfXyA9IFwiL1VzZXJzL3dvb2Qvd29ya3NwYWNlL2Z1bmJsb2Nrcy1leHRlbnNpb24vdHN1cC5jb25maWcudHNcIjtjb25zdCBfX2luamVjdGVkX2Rpcm5hbWVfXyA9IFwiL1VzZXJzL3dvb2Qvd29ya3NwYWNlL2Z1bmJsb2Nrcy1leHRlbnNpb25cIjtjb25zdCBfX2luamVjdGVkX2ltcG9ydF9tZXRhX3VybF9fID0gXCJmaWxlOi8vL1VzZXJzL3dvb2Qvd29ya3NwYWNlL2Z1bmJsb2Nrcy1leHRlbnNpb24vdHN1cC5jb25maWcudHNcIjtpbXBvcnQgeyBkZWZpbmVDb25maWcgfSBmcm9tICd0c3VwJ1xuaW1wb3J0IHsgY29weSB9IGZyb20gJ2VzYnVpbGQtcGx1Z2luLWNvcHknXG5pbXBvcnQgeyBOb2RlTW9kdWxlc1BvbHlmaWxsUGx1Z2luIH0gZnJvbSAnQGVzYnVpbGQtcGx1Z2lucy9ub2RlLW1vZHVsZXMtcG9seWZpbGwnXG5cbmNvbnN0IHRhZyA9ICdmdW5ibG9ja3MtY29udGFpbmVyJ1xuXG5leHBvcnQgZGVmYXVsdCBkZWZpbmVDb25maWcoe1xuICBlbnRyeTogW1xuICAgICcuL3NyYy9jb250ZW50L2luZGV4LnRzeCcsXG4gICAgJy4vc3JjL29wdGlvbnMvaW5kZXgudHN4JyxcbiAgICAnLi9zcmMvcG9wdXAvaW5kZXgudHN4JyxcbiAgICAnLi9zcmMvYmFja2dyb3VuZC9pbmRleC50cycsXG4gIF0sXG4gIHRhcmdldDogJ2Nocm9tZTExMicsXG4gIGZvcm1hdDogJ2NqcycsXG4gIHNwbGl0dGluZzogZmFsc2UsXG4gIHNvdXJjZW1hcDogZmFsc2UsXG4gIGNsZWFuOiB0cnVlLFxuICBtaW5pZnk6IHRydWUsXG4gIGRlZmluZToge1xuICAgICdwcm9jZXNzLmVudi5OT0RFX0VOVic6IEpTT04uc3RyaW5naWZ5KHByb2Nlc3MuZW52Lk5PREVfRU5WKSxcbiAgICAnaW1wb3J0Lm1ldGEudXJsJzogSlNPTi5zdHJpbmdpZnkoJ2Nocm9tZS1leHRlbnNpb246Ly91bmRlZmluZWQvJyksXG4gICAgJ2ltcG9ydC5tZXRhLmVudic6IEpTT04uc3RyaW5naWZ5KHt9KSxcbiAgICAnaW1wb3J0Lm1ldGEnOiBKU09OLnN0cmluZ2lmeSh7IHVybDogJ2Nocm9tZS1leHRlbnNpb246Ly91bmRlZmluZWQvJywgZW52OiB7fSB9KSxcbiAgfSxcbiAgaW5qZWN0U3R5bGU6IChjc3MpID0+IHtcbiAgICByZXR1cm4gYFxuICAgICAgICB2YXIgc2V0RnVuQmxvY2tzU3R5bGUgPSBmdW5jdGlvbigpIHtcbiAgICAgICAgICB2YXIgc3R5bGUgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCdzdHlsZScpO1xuICAgICAgICAgIHN0eWxlLnR5cGUgPSAndGV4dC9jc3MnO1xuICAgICAgICAgIHN0eWxlLmlubmVySFRNTCA9ICR7Y3NzfTtcbiAgICAgICAgICBpZiAoIWNocm9tZS5ydW50aW1lLm9wZW5PcHRpb25zUGFnZSkge1xuICAgICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgdmFyIHJvb3QgPSBkb2N1bWVudC5nZXRFbGVtZW50c0J5VGFnTmFtZSgnJHt0YWd9JylbMF0uc2hhZG93Um9vdDtcbiAgICAgICAgICAgICAgICByb290LmFwcGVuZENoaWxkKHN0eWxlKTsgICAgXG4gICAgICAgICAgICAgIH0gY2F0Y2gge1xuICAgICAgICAgICAgICAgIHNldEZ1bkJsb2Nrc1N0eWxlKClcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSwgMTAwKVxuICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICB2YXIgcm9vdCA9IGRvY3VtZW50LmhlYWQgfHwgZG9jdW1lbnQuZ2V0RWxlbWVudHNCeVRhZ05hbWUoJ2hlYWQnKVswXTtcbiAgICAgICAgICAgIHJvb3QuYXBwZW5kQ2hpbGQoc3R5bGUpXG4gICAgICAgICAgfVxuICAgICAgICAgXG4gICAgICAgIH07XG4gICAgICAgIHNldEZ1bkJsb2Nrc1N0eWxlKCk7XG4gICAgICBgXG4gIH0sXG4gIG91dEV4dGVuc2lvbjogKCkgPT4gKHsganM6ICcuanMnIH0pLFxuICBlc2J1aWxkUGx1Z2luczogW1xuICAgIE5vZGVNb2R1bGVzUG9seWZpbGxQbHVnaW4oKSxcbiAgICBjb3B5KHtcbiAgICAgIGFzc2V0czogW1xuICAgICAgICB7XG4gICAgICAgICAgZnJvbTogWycuL3NyYy9vcHRpb25zL2luZGV4Lmh0bWwnXSxcbiAgICAgICAgICB0bzogWycuL29wdGlvbnMnXSxcbiAgICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAgIGZyb206IFsnLi9zcmMvcG9wdXAvaW5kZXguaHRtbCddLFxuICAgICAgICAgIHRvOiBbJy4vcG9wdXAnXSxcbiAgICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAgIGZyb206IFsnLi9ub2RlX21vZHVsZXMvYW5pbWF0ZS5jc3MvYW5pbWF0ZS5jc3MnXSxcbiAgICAgICAgICB0bzogWycuL2NvbnRlbnQnXSxcbiAgICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAgIGZyb206IFsnLi9zcmMvYXNzZXRzLyonXSxcbiAgICAgICAgICB0bzogWycuL2Fzc2V0cyddLFxuICAgICAgICB9LFxuICAgICAgXSxcbiAgICAgIHdhdGNoOiBwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nLFxuICAgIH0pLFxuICBdLFxufSlcbiJdLAogICJtYXBwaW5ncyI6ICI7QUFBeVEsU0FBUyxvQkFBb0I7QUFDdFMsU0FBUyxZQUFZO0FBQ3JCLFNBQVMsaUNBQWlDO0FBRTFDLElBQU0sTUFBTTtBQUVaLElBQU8sc0JBQVEsYUFBYTtBQUFBLEVBQzFCLE9BQU87QUFBQSxJQUNMO0FBQUEsSUFDQTtBQUFBLElBQ0E7QUFBQSxJQUNBO0FBQUEsRUFDRjtBQUFBLEVBQ0EsUUFBUTtBQUFBLEVBQ1IsUUFBUTtBQUFBLEVBQ1IsV0FBVztBQUFBLEVBQ1gsV0FBVztBQUFBLEVBQ1gsT0FBTztBQUFBLEVBQ1AsUUFBUTtBQUFBLEVBQ1IsUUFBUTtBQUFBLElBQ04sd0JBQXdCLEtBQUssVUFBVSxRQUFRLElBQUksUUFBUTtBQUFBLElBQzNELG1CQUFtQixLQUFLLFVBQVUsK0JBQStCO0FBQUEsSUFDakUsbUJBQW1CLEtBQUssVUFBVSxDQUFDLENBQUM7QUFBQSxJQUNwQyxlQUFlLEtBQUssVUFBVSxFQUFFLEtBQUssaUNBQWlDLEtBQUssQ0FBQyxFQUFFLENBQUM7QUFBQSxFQUNqRjtBQUFBLEVBQ0EsYUFBYSxDQUFDLFFBQVE7QUFDcEIsV0FBTztBQUFBO0FBQUE7QUFBQTtBQUFBLDhCQUltQjtBQUFBO0FBQUE7QUFBQTtBQUFBLDREQUk4QjtBQUFBO0FBQUE7QUFBQTtBQUFBO0FBQUE7QUFBQTtBQUFBO0FBQUE7QUFBQTtBQUFBO0FBQUE7QUFBQTtBQUFBO0FBQUEsRUFjMUQ7QUFBQSxFQUNBLGNBQWMsT0FBTyxFQUFFLElBQUksTUFBTTtBQUFBLEVBQ2pDLGdCQUFnQjtBQUFBLElBQ2QsMEJBQTBCO0FBQUEsSUFDMUIsS0FBSztBQUFBLE1BQ0gsUUFBUTtBQUFBLFFBQ047QUFBQSxVQUNFLE1BQU0sQ0FBQywwQkFBMEI7QUFBQSxVQUNqQyxJQUFJLENBQUMsV0FBVztBQUFBLFFBQ2xCO0FBQUEsUUFDQTtBQUFBLFVBQ0UsTUFBTSxDQUFDLHdCQUF3QjtBQUFBLFVBQy9CLElBQUksQ0FBQyxTQUFTO0FBQUEsUUFDaEI7QUFBQSxRQUNBO0FBQUEsVUFDRSxNQUFNLENBQUMsd0NBQXdDO0FBQUEsVUFDL0MsSUFBSSxDQUFDLFdBQVc7QUFBQSxRQUNsQjtBQUFBLFFBQ0E7QUFBQSxVQUNFLE1BQU0sQ0FBQyxnQkFBZ0I7QUFBQSxVQUN2QixJQUFJLENBQUMsVUFBVTtBQUFBLFFBQ2pCO0FBQUEsTUFDRjtBQUFBLE1BQ0EsT0FBTyxRQUFRLElBQUksYUFBYTtBQUFBLElBQ2xDLENBQUM7QUFBQSxFQUNIO0FBQ0YsQ0FBQzsiLAogICJuYW1lcyI6IFtdCn0K
