{
  "compilerOptions": {
    "target": "ES2022",
    "module": "ESNext",
    "allowJs": true,
    "outDir": "dist",
    "skipLibCheck": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "strict": false,
    "jsx": "react-jsx",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "baseUrl": "./",
    "paths": {
      "@/*": ["src/*"]
    },
  },
  "include": ["src/**/*"]
}


// {
//   "compilerOptions": {
//     "target": "ES2022",
//     "lib": [
//       "dom",
//       "dom.iterable",
//       "esnext"
//     ],
//     "allowJs": true,
//     "skipLibCheck": true,
//     "esModuleInterop": true,
//     "allowSyntheticDefaultImports": true,
//     "strict": false,
//     "forceConsistentCasingInFileNames": true,
//     "noFallthroughCasesInSwitch": true,
//     "module": "esnext",
//     "moduleResolution": "node",
//     "resolveJsonModule": true,
//     "isolatedModules": true,
//     "noEmit": true,
//     "jsx": "react-jsx",
//     "baseUrl": ".",
//     "paths": {
//       "@/*": ["./src/*"]
//     }
//   },
//   "include": ["src/**/*"],
//   "exclude": [
//     "node_modules"
//   ]
// }
