#!/usr/bin/env node

// Script to fix manifest.json paths for Chrome Extension development

const fs = require('fs')
const path = require('path')

function fixManifestPaths(manifestPath) {
  if (!fs.existsSync(manifestPath)) {
    console.log(`Manifest file not found: ${manifestPath}`)
    return
  }

  try {
    const manifestContent = fs.readFileSync(manifestPath, 'utf8')
    const manifest = JSON.parse(manifestContent)

    // Fix paths by removing 'dist/' prefix
    function fixPath(pathStr) {
      if (typeof pathStr === 'string' && pathStr.startsWith('dist/')) {
        return pathStr.replace('dist/', '')
      }
      return pathStr
    }

    // Fix action paths
    if (manifest.action) {
      if (manifest.action.default_popup) {
        manifest.action.default_popup = fixPath(manifest.action.default_popup)
      }
      if (manifest.action.defult_icon) {
        manifest.action.defult_icon = fixPath(manifest.action.defult_icon)
      }
    }

    // Fix options_ui path
    if (manifest.options_ui && manifest.options_ui.page) {
      manifest.options_ui.page = fixPath(manifest.options_ui.page)
    }

    // Fix icons paths
    if (manifest.icons) {
      Object.keys(manifest.icons).forEach((size) => {
        manifest.icons[size] = fixPath(manifest.icons[size])
      })
    }

    // Fix content_scripts paths
    if (manifest.content_scripts) {
      manifest.content_scripts.forEach((script) => {
        if (script.js) {
          script.js = script.js.map(fixPath)
        }
        if (script.css) {
          script.css = script.css.map(fixPath)
        }
      })
    }

    // Fix background service_worker path
    if (manifest.background && manifest.background.service_worker) {
      manifest.background.service_worker = fixPath(
        manifest.background.service_worker
      )
    }

    // Fix web_accessible_resources paths
    if (manifest.web_accessible_resources) {
      manifest.web_accessible_resources.forEach((resource) => {
        if (resource.resources) {
          resource.resources = resource.resources.map(fixPath)
        }
      })
    }

    // Write the fixed manifest back
    const fixedContent = JSON.stringify(manifest, null, 2)
    fs.writeFileSync(manifestPath, fixedContent, 'utf8')

    console.log(`✅ Fixed manifest paths in: ${manifestPath}`)
  } catch (error) {
    console.error(
      `❌ Error fixing manifest paths in ${manifestPath}:`,
      error.message
    )
  }
}

// Get the product from environment variable or command line argument
const product = process.env.PRODUCT || process.argv[2] || 'funblocks'
const outputDir = product === 'mindmap' ? 'dist-mindmap' : 'dist-funblocks'
const manifestPath = path.join(outputDir, 'manifest.json')

console.log(`🔧 Fixing manifest paths for ${product} product...`)
fixManifestPaths(manifestPath)
