#!/bin/bash

# Multi-product build script for Chrome Extensions

set -e

echo "🚀 Starting multi-product build process..."

# Clean up previous builds
echo "🧹 Cleaning up previous builds..."
rm -rf release/
mkdir -p release

# Build FunBlocks AI Extension
echo "📦 Building FunBlocks AI Extension..."
npm run build:funblocks

# Package FunBlocks
echo "📦 Packaging FunBlocks AI Extension..."
mkdir -p release/funblocks
cp -rp dist-funblocks/* release/funblocks/
cd release/funblocks
zip -r ../funblocks-ai-extension.zip *
cd ../..

# Build AI MindMap Extension
echo "🧠 Building AI MindMap Extension..."
npm run build:mindmap

# Package AI MindMap
echo "📦 Packaging AI MindMap Extension..."
mkdir -p release/mindmap
cp -rp dist-mindmap/* release/mindmap/
cd release/mindmap
zip -r ../ai-mindmap-extension.zip *
cd ../..

echo "✅ Multi-product build completed successfully!"
echo ""
echo "📁 Build artifacts:"
echo "   - FunBlocks AI Extension: release/funblocks-ai-extension.zip"
echo "   - AI MindMap Extension: release/ai-mindmap-extension.zip"
echo ""
echo "🎉 Ready for deployment!"
