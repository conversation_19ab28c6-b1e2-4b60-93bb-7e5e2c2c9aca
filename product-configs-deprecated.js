// Product configurations for multi-product build system

const productConfigs = {
  funblocks: {
    name: 'FunBlocks AI Extension',
    manifestName: 'FunBlocks AI - Your Ultimate Writing and Reading Copilot',
    description:
      'Available anywhere. Write emails, optimize text, assist with writing, read articles, answer complex questions, boost thinking skills',
    version: '4.1.0',
    icon: 'icon.png',
    outputDir: 'dist-funblocks',
    features: {
      // All features enabled for FunBlocks
      sidebarMenu: {
        ai_flow_question: true,
        ai_flow_explore: true,
        assistant_with_page_context: true,
        assistant_without_page_context: true,
        quick_settings: true,
        screen_capture: true,
        all_features: true,
      },
      widgets: {
        youtube: {
          enabled: true,
          showSummaryButton: true,
        },
        reply: true,
        aiWriter: true,
        codesExplain: true,
        allWidgets: true,
      },
      contextualMenu: true,
      write_assistant: true,
      popup: true,
      options: true,
    },
  },
  mindmap: {
    name: 'AI MindMap Extension',
    manifestName: 'AI MindMap - Smart Mind Mapping Assistant',
    description:
      'AI-powered mind mapping and content exploration tool for enhanced thinking and learning',
    version: '1.0.0',
    icon: 'mindmap-icon.png',
    outputDir: 'dist-mindmap',
    features: {
      // Limited features for AI MindMap
      sidebarMenu: {
        ai_flow_question: true,
        ai_flow_explore: true,
        assistant_with_page_context: true,
        assistant_without_page_context: false,
        quick_settings: false,
        screen_capture: false,
        all_features: false,
      },
      widgets: {
        youtube: {
          enabled: true,
          showSummaryButton: false, // Hide summary button for mindmap
        },
        reply: false,
        aiWriter: false,
        codesExplain: false,
        allWidgets: false,
      },
      contextualMenu: false,
      write_assistant: false,
      popup: true,
      options: true,
    },
  },
}

// Get current product config based on environment variable
function getCurrentProductConfig() {
  const product = process.env.PRODUCT || 'funblocks'
  return productConfigs[product]
}

// Check if a feature is enabled for current product
function isFeatureEnabled(featurePath) {
  const config = getCurrentProductConfig()
  const pathParts = featurePath.split('.')
  let current = config.features

  for (const part of pathParts) {
    if (current && typeof current === 'object' && part in current) {
      current = current[part]
    } else {
      return false
    }
  }

  return Boolean(current)
}

module.exports = {
  productConfigs,
  getCurrentProductConfig,
  isFeatureEnabled,
}
