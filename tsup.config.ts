import { defineConfig } from 'tsup'
import { copy } from 'esbuild-plugin-copy'
import { NodeModulesPolyfillPlugin } from '@esbuild-plugins/node-modules-polyfill'
import { getCurrentProductConfig } from './product-configs-deprecated.js'

const productConfig = getCurrentProductConfig()
const tag = `${process.env.PRODUCT || 'funblocks'}-container`

export default defineConfig({
  entry: [
    './src/content/index.tsx',
    './src/options/index.tsx',
    './src/popup/index.tsx',
    './src/background/index.ts',
  ],
  target: 'chrome112',
  format: 'cjs',
  splitting: false,
  sourcemap: false,
  clean: true,
  minify: true,
  define: {
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV),
    'process.env.PRODUCT': JSON.stringify(process.env.PRODUCT || 'funblocks'),
    'import.meta.url': JSON.stringify('chrome-extension://undefined/'),
    'import.meta.env': JSON.stringify({}),
    'import.meta': JSON.stringify({
      url: 'chrome-extension://undefined/',
      env: {},
    }),
  },
  injectStyle: (css) => {
    return `
        var setFunBlocksStyle = function() {
          var style = document.createElement('style');
          style.type = 'text/css';
          style.innerHTML = ${css};
          if (!chrome.runtime.openOptionsPage) {
            setTimeout(() => {
              try {
                var root = document.getElementsByTagName('${tag}')[0].shadowRoot;
                root.appendChild(style);    
              } catch {
                setFunBlocksStyle()
              }
            }, 100)
          } else {
            var root = document.head || document.getElementsByTagName('head')[0];
            root.appendChild(style)
          }
         
        };
        setFunBlocksStyle();
      `
  },
  outExtension: () => ({ js: '.js' }),
  outDir: productConfig.outputDir,
  esbuildPlugins: [
    NodeModulesPolyfillPlugin(),
    copy({
      assets: [
        {
          from: ['./src/options/index.html'],
          to: ['./options'],
        },
        {
          from: ['./src/popup/index.html'],
          to: ['./popup'],
        },
        {
          from: ['./node_modules/animate.css/animate.css'],
          to: ['./content'],
        },
        {
          from: ['./src/assets/*'],
          to: ['./assets'],
        },
        // Copy the appropriate manifest file based on product
        {
          from: [
            process.env.PRODUCT === 'mindmap'
              ? './manifest-mindmap.json'
              : './manifest.json',
          ],
          to: ['./manifest.json'],
        },
      ],
      watch: process.env.NODE_ENV !== 'production',
    }),
  ],
})
